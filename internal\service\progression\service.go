package progression

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/cache"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	_progress "github.com/dsoplabs/dinbora-backend/internal/repository/progression"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing/contract"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/trail"
	"github.com/dsoplabs/dinbora-backend/internal/service/gamification"
	"github.com/dsoplabs/dinbora-backend/internal/service/vault"
	// Added for model.Vault in helpers.go context
)

type Service interface {
	// CRUD
	Initialize(ctx context.Context, userId string) error
	Find(ctx context.Context, id string) (*progression.Progression, error)
	FindByUser(ctx context.Context, userId string) (*progression.Progression, error)
	Update(ctx context.Context, progression *progression.Progression) error
	Delete(ctx context.Context, id string) error

	// Trail CRUD
	FindTrail(ctx context.Context, userId string, userClassification string, contractIDs []string, trailId string) (*progression.Trail, error)

	// Lesson CRUD
	CreateLesson(ctx context.Context, userId string, userClassification string, contractIDs []string, progression *progression.ProgressionBody) error
	FindLesson(ctx context.Context, userId string, userClassification string, contractIDs []string, trailId string, lessonId string) (*progression.Lesson, error)

	// Challenge CRUD
	CreateChallenge(ctx context.Context, userId string, userClassification string, contractIDs []string, progression *progression.ProgressionBody) error
	FindChallenge(ctx context.Context, userId string, userClassification string, contractIDs []string, trailId string, challengeId string) (*progression.Challenge, error)
	FindChallengePhase(ctx context.Context, userId string, userClassification string, contractIDs []string, trailId string, challengeId string, challengePhase string) (*progression.ChallengePhase, error)
	FindChallengePhaseCurrentPoints(ctx context.Context, userId string, userClassification string, contractIDs []string, trailId string, challengeId string, phaseId string) (int, error)

	// Card CRUD
	FindTrailCards(ctx context.Context, userId string, userClassification string, contractIDs []string, trailId string) ([]*progression.TrailCard, error)
	FindRegularTrailCards(ctx context.Context, userId string, trailId string) ([]*progression.TrailCard, error)
	FindExtraTrailCards(ctx context.Context, userId string, userClassification string, contractIDs []string, trailId string) ([]*progression.TrailCard, error)
	FindModuleCards(ctx context.Context, userId string, userClassification string, contractIDs []string, trailId string, lessonId string) ([]*progression.LessonCard, *progression.ChallengeCard, error)
	FindChallengePhaseCards(ctx context.Context, userId string, userClassification string, contractIDs []string, trailId string, phaseId string) ([]*progression.ChallengePhaseCard, error)

	LegacyCreateLessonChallenge(ctx context.Context, userId string, userClassification string, contractIDs []string, progression *progression.ProgressionBody) error
}

type service struct {
	Repository          _progress.Repository
	TrailService        trail.Service
	VaultService        vault.Service
	ContractService     contract.Service
	Cache               cache.CacheService   // Correct type: CacheService
	GamificationService gamification.Service // Added gamification service
}

func New(repository _progress.Repository, trailService trail.Service, vaultService vault.Service, contractService contract.Service, cache cache.CacheService, gamificationService gamification.Service) Service {
	return &service{
		Repository:          repository,
		TrailService:        trailService,
		VaultService:        vaultService,
		ContractService:     contractService,
		Cache:               cache, // Initialize cache dependency
		GamificationService: gamificationService,
	}
}

// CRUD
func (s *service) Initialize(ctx context.Context, userId string) error {
	foundProgress, err := s.FindByUser(ctx, userId)
	if err != nil && err.(*errors.DomainError).Kind() != errors.NotFound {
		return errors.New(errors.Service, "failed to initialize progress service", errors.Internal, err)
	} else if foundProgress != nil {
		return err
	}

	currentTime := time.Now()
	newProgression := &progression.Progression{
		User:      userId,
		Trails:    nil,
		CreatedAt: currentTime,
		UpdatedAt: currentTime,
	}

	// Invalidate cache if initialization occurs after potential previous entry
	s.invalidateUserProgressionCache(ctx, userId)
	return s.Repository.Create(ctx, newProgression)
}

func (s *service) Find(ctx context.Context, id string) (*progression.Progression, error) {
	foundProgression, err := s.Repository.Find(ctx, id)
	if err != nil {
		return nil, err
	}
	foundProgression.ID = foundProgression.ObjectID.Hex()
	return foundProgression, nil
}

func (s *service) FindByUser(ctx context.Context, userId string) (*progression.Progression, error) {
	cacheKey := fmt.Sprintf("progression:user:%s", userId)

	// Check cache
	cachedData, found := s.Cache.Get(ctx, cacheKey)
	if found {
		if p, ok := cachedData.(*progression.Progression); ok {
			log.Printf("Cache hit for FindByUser progression for user ID: %s", userId)
			return p, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion to *progression.Progression failed", cacheKey)
	} else {
		log.Printf("Cache miss for FindByUser progression for user ID: %s. Fetching from database...", userId)
	}

	// Cache miss or error, fetch from repo
	foundProgression, err := s.Repository.FindByUser(ctx, userId)
	if err != nil {
		if domainErr, ok := err.(*errors.DomainError); ok && domainErr.Kind() == errors.NotFound {
			return nil, err
		}
		log.Printf("Error fetching progression for user %s from repo: %v", userId, err)
		return nil, err
	}
	foundProgression.ID = foundProgression.ObjectID.Hex()

	// Populate cache
	err = s.Cache.Set(ctx, cacheKey, foundProgression, time.Hour)
	if err != nil {
		log.Printf("Error setting cache for FindByUser progression for user ID %s: %v", userId, err)
	} else {
		log.Printf("Cache populated for FindByUser progression for user ID: %s", userId)
	}

	return foundProgression, nil
}

func (s *service) Update(ctx context.Context, progression *progression.Progression) error {
	err := s.Repository.Update(ctx, progression)
	if err == nil {
		// Invalidate cache on successful update
		s.invalidateUserProgressionCache(ctx, progression.User)
	}
	return err
}

func (s *service) Delete(ctx context.Context, id string) error {
	progToDelete, err := s.Find(ctx, id)
	if err != nil {
		if err.(*errors.DomainError).Kind() == errors.NotFound {
			return nil
		}
		return errors.New(errors.Service, "failed to find progression before delete", errors.Internal, err)
	}
	userId := progToDelete.User

	err = s.Repository.Delete(ctx, id)
	if err == nil {
		// Invalidate cache on successful delete
		s.invalidateUserProgressionCache(ctx, userId)
	}
	return err
}

// Trail CRUD

// Lesson CRUD

func (s *service) CreateLesson(ctx context.Context, userId string, userClassification string, contractIDs []string, progressionBody *progression.ProgressionBody) error {
	if progressionBody.Type != string(progression.ProgressionTypeLesson) {
		return errors.New(errors.Service, "invalid progression type for lesson progress", errors.Validation, nil)
	}

	// Invalidate cache before potential update
	s.invalidateUserProgressionCache(ctx, userId)

	userProgression, userVault, trailContent, err := s.loadUserData(ctx, userId, userClassification, contractIDs, progressionBody)
	if err != nil {
		return err
	}

	if err = s.validateAndPrepareProgression(ctx, userProgression, progressionBody, trailContent); err != nil {
		return err
	}

	userProgression.Register(progressionBody, trailContent)
	updateVault := false

	lessonContent := trailContent.GetLesson(progressionBody.Module)
	if lessonContent == nil {
		return errors.New(errors.Service, "lesson not found", errors.NotFound, nil)
	}

	userProgression.UpdateLessonStatus(progressionBody, lessonContent)
	userProgression.UpdateTrailTotal(trailContent)

	lessonProgress := userProgression.GetLessonProgression(trailContent.ID, progressionBody.Module)
	if lessonProgress != nil && lessonProgress.Completed {
		if lessonProgress.Current == string(progression.RewardTypeCoin) && !lessonProgress.Rewarded {
			userVault.Coins += LessonCompletionReward
			lessonProgress.Rewarded = true
			updateVault = true
		}
	}

	trailUpdateNeeded, err := s.checkTrailCompletionAndAwardCoin(ctx, userProgression, userVault, trailContent)
	if err != nil {
		log.Println("error checking trail completion:", err)
	}
	updateVault = updateVault || trailUpdateNeeded

	if err = s.updateUserVaultIfNeeded(ctx, userVault, updateVault); err != nil {
		return err
	}

	return s.Update(ctx, userProgression)
}

func (s *service) FindLesson(ctx context.Context, userId string, userClassification string, contractIDs []string, trailId string, lessonId string) (*progression.Lesson, error) {
	// Use the cached service method instead of the repository directly
	foundProgression, err := s.FindByUser(ctx, userId)
	if err != nil {
		return nil, err
	}
	trailContent, err := s.TrailService.Find(ctx, trailId)
	if err != nil {
		return nil, err
	}

	if !trailContent.HasAccess(userClassification, contractIDs) {
		return nil, errors.New(errors.Service, "access denied", errors.Forbidden, nil)
	}

	lessonContent := trailContent.GetLesson(lessonId)
	if lessonContent == nil {
		return nil, errors.New(errors.Service, "lesson not found", errors.NotFound, nil)
	}

	return getLessonProgress(foundProgression, trailId, lessonContent), nil
}

// Challenge CRUD

func (s *service) CreateChallenge(ctx context.Context, userId string, userClassification string, contractIDs []string, progressionBody *progression.ProgressionBody) error {
	if progressionBody.Type != string(progression.ProgressionTypeChallenge) {
		return errors.New(errors.Service, "invalid progression type for challenge progress", errors.Validation, nil)
	}

	// Invalidate cache before potential update
	s.invalidateUserProgressionCache(ctx, userId)

	userProgression, userVault, trailContent, err := s.loadUserData(ctx, userId, userClassification, contractIDs, progressionBody)
	if err != nil {
		return err
	}

	if err = s.validateAndPrepareProgression(ctx, userProgression, progressionBody, trailContent); err != nil {
		return err
	}

	userProgression.Register(progressionBody, trailContent)
	updateVault := false

	if trailContent.Challenge == nil {
		return errors.New(errors.Service, "challenge not found", errors.NotFound, nil)
	}

	userProgression.UpdateChallengeStatus(progressionBody, trailContent.Challenge)
	userProgression.UpdateChallengeTotal(trailContent)
	userProgression.UpdateTrailTotal(trailContent)

	challengeProgress := userProgression.GetChallengeProgression(trailContent.ID)
	if challengeProgress != nil && challengeProgress.Completed && !challengeProgress.Rewarded {
		userVault.Coins += ChallengeCompletionReward
		challengeProgress.Rewarded = true
		updateVault = true
	}

	trailUpdateNeeded, err := s.checkTrailCompletionAndAwardCoin(ctx, userProgression, userVault, trailContent)
	if err != nil {
		log.Println("error checking trail completion:", err)
	}
	updateVault = updateVault || trailUpdateNeeded

	if err = s.updateUserVaultIfNeeded(ctx, userVault, updateVault); err != nil {
		return err
	}

	// Update progression first
	if err = s.Update(ctx, userProgression); err != nil {
		return err
	}

	// Check for achievements after successful progression update
	if s.GamificationService != nil {
		if err := s.GamificationService.CheckAchievements(ctx, userId); err != nil {
			// Log error but don't fail the progression update
			log.Printf("Failed to check achievements for user %s: %v", userId, err)
		}
	}

	return nil
}

func (s *service) FindTrail(ctx context.Context, userId string, userClassification string, contractIDs []string, trailId string) (*progression.Trail, error) {
	// Use the cached service method instead of the repository directly
	foundProgression, err := s.FindByUser(ctx, userId)
	if err != nil {
		return nil, err
	}
	trailContent, err := s.TrailService.Find(ctx, trailId)
	if err != nil {
		return nil, err
	}

	if !trailContent.HasAccess(userClassification, contractIDs) {
		return nil, errors.New(errors.Service, "access denied", errors.Forbidden, nil)
	}

	return getTrailProgress(foundProgression, trailContent), nil
}

func (s *service) FindChallenge(ctx context.Context, userId string, userClassification string, contractIDs []string, trailId string, challengeId string) (*progression.Challenge, error) {
	// Use the cached service method instead of the repository directly
	foundProgression, err := s.FindByUser(ctx, userId)
	if err != nil {
		return nil, err
	}

	trailContent, err := s.TrailService.Find(ctx, trailId)
	if err != nil {
		return nil, err
	}
	if !trailContent.HasAccess(userClassification, contractIDs) {
		return nil, errors.New(errors.Service, "access denied", errors.Forbidden, nil)
	}

	challengeContent := trailContent.Challenge
	if challengeContent == nil || challengeContent.Identifier != challengeId {
		return nil, errors.New(errors.Service, "challenge not found", errors.NotFound, nil)
	}

	return getChallengeProgress(foundProgression, trailContent, challengeContent), nil
}

func (s *service) FindChallengePhase(ctx context.Context, userId string, userClassification string, contractIDs []string, trailId string, challengeId string, challengePhase string) (*progression.ChallengePhase, error) {
	// Use the cached service method instead of the repository directly
	foundProgression, err := s.FindByUser(ctx, userId)
	if err != nil {
		return nil, err
	}

	trailContent, err := s.TrailService.Find(ctx, trailId)
	if err != nil {
		return nil, err
	}
	if !trailContent.HasAccess(userClassification, contractIDs) {
		return nil, errors.New(errors.Service, "access denied", errors.Forbidden, nil)
	}

	challengeContent := trailContent.Challenge
	if challengeContent == nil || challengeContent.Identifier != challengeId {
		return nil, errors.New(errors.Service, "challenge not found", errors.NotFound, nil)
	}

	phaseContent := challengeContent.GetPhase(challengePhase)
	if phaseContent == nil {
		return nil, errors.New(errors.Service, "challenge phase not found", errors.NotFound, nil)
	}

	return getChallengePhaseProgress(foundProgression, trailContent, challengeContent, phaseContent), nil
}

func (s *service) FindChallengePhaseCurrentPoints(ctx context.Context, userId string, userClassification string, contractIDs []string, trailId string, challengeId string, phaseId string) (int, error) {
	// Use the cached service method instead of the repository directly
	foundProgression, err := s.FindByUser(ctx, userId)
	if err != nil {
		return 0, err
	}

	trailContent, err := s.TrailService.Find(ctx, trailId)
	if err != nil {
		return 0, err
	}
	if !trailContent.HasAccess(userClassification, contractIDs) {
		return 0, errors.New(errors.Service, "access denied", errors.Forbidden, nil)
	}

	challengeContent := trailContent.Challenge
	if challengeContent == nil || challengeContent.Identifier != challengeId {
		return 0, errors.New(errors.Service, "challenge not found", errors.NotFound, nil)
	}

	phaseContent := challengeContent.GetPhase(phaseId)
	if phaseContent == nil {
		return 0, errors.New(errors.Service, "challenge phase not found", errors.NotFound, nil)
	}

	challengePhaseProgress := getChallengePhaseProgress(foundProgression, trailContent, challengeContent, phaseContent)
	if challengePhaseProgress == nil {
		// This case should ideally be handled by getChallengePhaseProgress returning an error or a default struct
		// For now, assume if progress is nil, points are 0 or it's an error state.
		// Depending on business logic, might return an error instead.
		return 0, errors.New(errors.Service, "challenge phase progress not found", errors.NotFound, nil)
	}

	return challengePhaseProgress.CurrentPoints, nil
}

// Card CRUD
func (s *service) FindTrailCards(ctx context.Context, userId string, userClassification string, contractIDs []string, trailId string) ([]*progression.TrailCard, error) {
	// --- 1. Get User Trail Progressions (optimized) ---
	trailProgressMap, err := s.Repository.FindForCards(ctx, userId)
	if err != nil {
		log.Printf("Error finding trail progressions for user %s: %v", userId, err)
		return nil, err // Propagate error
	}

	// --- 2. Fetch Card Data ---
	var fetchedCardDataList []*content.TrailCard
	var trailServiceErr error

	if trailId == "" {
		// Get lightweight card data for all trails
		fetchedCardDataList, trailServiceErr = s.TrailService.FindAllCardData(ctx)
		// Filter for accessible trails
		fetchedCardDataList = filterAccessibleTrails(fetchedCardDataList, userClassification, contractIDs)
	} else {
		// Get lightweight card data for a specific trail
		var singleCardData *content.TrailCard
		singleCardData, trailServiceErr = s.TrailService.FindCardData(ctx, trailId)
		if singleCardData != nil && !singleCardData.HasAccess(userClassification, contractIDs) {
			return nil, errors.New(errors.Service, "access denied", errors.Forbidden, nil)
		}
		if trailServiceErr == nil && singleCardData != nil {
			// Put the single result into a slice to unify processing
			fetchedCardDataList = []*content.TrailCard{singleCardData}
		}
		// If singleCardData is nil (and no error), fetchedCardDataList remains nil/empty, handled below
	}

	// Handle errors from trail service calls
	if trailServiceErr != nil {
		return nil, trailServiceErr // Propagate error
	}

	// --- 3. Process Card Data ---
	// Handle cases where no data was found (empty list or nil single result)
	if len(fetchedCardDataList) == 0 {
		return nil, nil // Return empty, no error
	}

	// Pre-allocate the results slice
	cards := make([]*progression.TrailCard, 0, len(fetchedCardDataList))
	var totalCardCreationTime time.Duration // Accumulate time spent creating cards

	// Process each card data uniformly
	for _, cardData := range fetchedCardDataList {
		startCardCreation := time.Now()

		// Create the trail card
		card := &progression.TrailCard{
			ID:         cardData.ID,
			Name:       cardData.Name,
			Identifier: cardData.Identifier,
			Level:      cardData.Level,
			Logo:       cardData.Logo,
			Color:      cardData.Color,
		}

		// Add progression data if available
		if trailProgress, exists := trailProgressMap[cardData.ID]; exists {
			card.Total = trailProgress.Total
			card.Available = trailProgress.Available
			card.Current = trailProgress.Current
		} else {
			// Default values for new trails
			card.Available = true // Assume available unless requirements say otherwise
			// Check if trail has requirements that would make it unavailable
			if len(cardData.Requirements) > 0 {
				for _, reqID := range cardData.Requirements {
					if reqProgress, reqExists := trailProgressMap[reqID]; !reqExists || reqProgress.Total < 90 {
						card.Available = false
						break
					}
				}
			}
		}

		cards = append(cards, card)
		totalCardCreationTime += time.Since(startCardCreation)
	}

	return cards, nil
}

func (s *service) FindRegularTrailCards(ctx context.Context, userId string, trailId string) ([]*progression.TrailCard, error) {
	// --- 1. Get User Trail Progressions (optimized) ---
	trailProgressMap, err := s.Repository.FindForCards(ctx, userId)
	if err != nil {
		log.Printf("Error finding trail progressions for user %s: %v", userId, err)
		return nil, err // Propagate error
	}

	// --- 2. Fetch Card Data ---
	var fetchedCardDataList []*content.TrailCard
	var trailServiceErr error

	if trailId == "" {
		// Get lightweight card data for all trails
		fetchedCardDataList, trailServiceErr = s.TrailService.FindAllRegularTrailsCardData(ctx)
		// Access control is removed as per simplification
	} else {
		// Get lightweight card data for a specific trail
		var singleCardData *content.TrailCard
		singleCardData, trailServiceErr = s.TrailService.FindRegularTrailCardData(ctx, trailId)
		// Access control is removed as per simplification
		if trailServiceErr == nil && singleCardData != nil {
			// Put the single result into a slice to unify processing
			fetchedCardDataList = []*content.TrailCard{singleCardData}
		}
		// If singleCardData is nil (and no error), fetchedCardDataList remains nil/empty, handled below
	}

	// Handle errors from trail service calls
	if trailServiceErr != nil {
		return nil, trailServiceErr // Propagate error
	}

	// --- 3. Process Card Data ---
	// Handle cases where no data was found (empty list or nil single result)
	if len(fetchedCardDataList) == 0 {
		return nil, nil // Return empty, no error
	}

	// Pre-allocate the results slice
	cards := make([]*progression.TrailCard, 0, len(fetchedCardDataList))
	var totalCardCreationTime time.Duration // Accumulate time spent creating cards

	// Process each card data uniformly
	for _, cardData := range fetchedCardDataList {
		startCardCreation := time.Now()

		// Create the trail card
		card := &progression.TrailCard{
			ID:         cardData.ID,
			Name:       cardData.Name,
			Identifier: cardData.Identifier,
			Level:      cardData.Level,
			Logo:       cardData.Logo,
			Color:      cardData.Color,
		}

		// Add progression data if available
		if trailProgress, exists := trailProgressMap[cardData.ID]; exists {
			card.Total = trailProgress.Total
			card.Available = trailProgress.Available
			card.Current = trailProgress.Current
		} else {
			// Default values for new trails
			card.Available = true // Assume available unless requirements say otherwise
			// Check if trail has requirements that would make it unavailable
			if len(cardData.Requirements) > 0 {
				for _, reqID := range cardData.Requirements {
					if reqProgress, reqExists := trailProgressMap[reqID]; !reqExists || reqProgress.Total < 90 {
						card.Available = false
						break
					}
				}
			}
		}

		cards = append(cards, card)
		totalCardCreationTime += time.Since(startCardCreation)
	}

	return cards, nil
}

func (s *service) FindExtraTrailCards(ctx context.Context, userId string, userClassification string, contractIDs []string, trailId string) ([]*progression.TrailCard, error) {
	// --- 1. Get User Trail Progressions (optimized) ---
	trailProgressMap, err := s.Repository.FindForCards(ctx, userId)
	if err != nil {
		log.Printf("Error finding trail progressions for user %s: %v", userId, err)
		return nil, err // Propagate error
	}

	// --- 2. Fetch Card Data ---
	var fetchedCardDataList []*content.TrailCard
	var trailServiceErr error

	if trailId == "" {
		// Get lightweight card data for all trails
		fetchedCardDataList, trailServiceErr = s.TrailService.FindAllExtraTrailsCardData(ctx, userClassification, contractIDs)
		// Filter for accessible trails
		fetchedCardDataList = filterAccessibleTrails(fetchedCardDataList, userClassification, contractIDs)
	} else {
		// Get lightweight card data for a specific trail
		var singleCardData *content.TrailCard
		singleCardData, trailServiceErr = s.TrailService.FindExtraTrailCardData(ctx, trailId, userClassification, contractIDs)
		if singleCardData != nil && !singleCardData.HasAccess(userClassification, contractIDs) {
			return nil, errors.New(errors.Service, "access denied", errors.Forbidden, nil)
		}
		if trailServiceErr == nil && singleCardData != nil {
			// Put the single result into a slice to unify processing
			fetchedCardDataList = []*content.TrailCard{singleCardData}
		}
		// If singleCardData is nil (and no error), fetchedCardDataList remains nil/empty, handled below
	}

	// Handle errors from trail service calls
	if trailServiceErr != nil {
		return nil, trailServiceErr // Propagate error
	}

	// --- 3. Process Card Data ---
	// Handle cases where no data was found (empty list or nil single result)
	if len(fetchedCardDataList) == 0 {
		return nil, nil // Return empty, no error
	}

	// Pre-allocate the results slice
	cards := make([]*progression.TrailCard, 0, len(fetchedCardDataList))
	var totalCardCreationTime time.Duration // Accumulate time spent creating cards

	// Process each card data uniformly
	for _, cardData := range fetchedCardDataList {
		startCardCreation := time.Now()

		// Create the trail card
		card := &progression.TrailCard{
			ID:         cardData.ID,
			Name:       cardData.Name,
			Identifier: cardData.Identifier,
			Level:      cardData.Level,
			Logo:       cardData.Logo,
			Color:      cardData.Color,
		}

		// Add progression data if available
		if trailProgress, exists := trailProgressMap[cardData.ID]; exists {
			card.Total = trailProgress.Total
			card.Available = trailProgress.Available
			card.Current = trailProgress.Current
		} else {
			// Default values for new trails
			card.Available = true // Assume available unless requirements say otherwise
			// Check if trail has requirements that would make it unavailable
			if len(cardData.Requirements) > 0 {
				for _, reqID := range cardData.Requirements {
					if reqProgress, reqExists := trailProgressMap[reqID]; !reqExists || reqProgress.Total < 90 {
						card.Available = false
						break
					}
				}
			}
		}

		cards = append(cards, card)
		totalCardCreationTime += time.Since(startCardCreation)
	}

	return cards, nil
}

func (s *service) FindModuleCards(ctx context.Context, userId string, userClassification string, contractIDs []string, trailId string, lessonId string) ([]*progression.LessonCard, *progression.ChallengeCard, error) {
	// Use the cached service method instead of the repository directly
	foundProgression, err := s.FindByUser(ctx, userId)
	if err != nil {
		return nil, nil, err
	}
	var contents []*content.Lesson
	var cards []*progression.LessonCard
	var challengeCard *progression.ChallengeCard

	foundContent, err := s.TrailService.Find(ctx, trailId)
	if err != nil {
		return nil, nil, err
	} else if foundContent == nil {
		return nil, nil, nil
	} else if !foundContent.HasAccess(userClassification, contractIDs) {
		return nil, nil, errors.New(errors.Service, "access denied", errors.Forbidden, nil)
	}

	if lessonId == "" {
		contents = foundContent.Lessons
	} else {
		contents = append(contents, foundContent.GetLesson(lessonId))
	}

	for _, foundLesson := range contents {
		foundTrailProgress := getLessonProgress(foundProgression, trailId, foundLesson)
		cards = append(cards, foundTrailProgress.ToCard(foundLesson))
	}

	if foundContent.Challenge != nil {
		challengeProgress := getChallengeProgress(foundProgression, foundContent, foundContent.Challenge)
		if challengeProgress != nil {
			challengeCard = challengeProgress.ToCard(foundContent.Challenge)
		}
	}

	return cards, challengeCard, nil
}

func (s *service) FindChallengePhaseCards(ctx context.Context, userId string, userClassification string, contractIDs []string, trailId string, phaseId string) ([]*progression.ChallengePhaseCard, error) {
	// Use the cached service method instead of the repository directly
	foundProgression, err := s.FindByUser(ctx, userId)
	if err != nil {
		return nil, err
	}
	var contents []*content.ChallengePhase

	var cards []*progression.ChallengePhaseCard

	foundContent, err := s.TrailService.Find(ctx, trailId)
	if err != nil {
		return nil, err
	} else if foundContent == nil || foundContent.Challenge == nil {
		return nil, nil
	} else if !foundContent.HasAccess(userClassification, contractIDs) {
		return nil, errors.New(errors.Service, "access denied", errors.Forbidden, nil)
	}

	if phaseId == "" {
		contents = foundContent.Challenge.Phases
	} else {
		contents = append(contents, foundContent.Challenge.GetPhase(phaseId))
	}

	for _, foundPhase := range contents {
		foundPhaseProgression := getChallengePhaseProgress(foundProgression, foundContent, foundContent.Challenge, foundPhase)
		if foundPhaseProgression.Completed {
			foundPhaseProgression.Rewarded = true
		}
		cards = append(cards, foundPhaseProgression.ToCard(foundPhase))
	}

	return cards, nil
}

// Legacy

func (s *service) LegacyCreateLessonChallenge(ctx context.Context, userId string, userClassification string, contractIDs []string, progressionBody *progression.ProgressionBody) error {
	// Invalidate cache before potential update
	s.invalidateUserProgressionCache(ctx, userId)

	userProgression, userVault, trailContent, err := s.loadUserData(ctx, userId, userClassification, contractIDs, progressionBody)
	if err != nil {
		return err
	}

	if err = s.validateAndPrepareProgression(ctx, userProgression, progressionBody, trailContent); err != nil {
		return err
	}

	userProgression.Register(progressionBody, trailContent)
	updateVault := false

	if progressionBody.Type == string(progression.ProgressionTypeLesson) {
		lessonContent := trailContent.GetLesson(progressionBody.Module)
		if lessonContent == nil {
			return errors.New(errors.Service, "lesson not found", errors.NotFound, nil)
		}

		userProgression.UpdateLessonStatus(progressionBody, lessonContent)
		userProgression.UpdateTrailTotal(trailContent)

		lessonProgress := userProgression.GetLessonProgression(trailContent.ID, progressionBody.Module)
		if lessonProgress != nil && lessonProgress.Completed {
			if lessonProgress.Current == string(progression.RewardTypeCoin) && !lessonProgress.Rewarded {
				userVault.Coins += LessonCompletionReward
				updateVault = true
				lessonProgress.Rewarded = true
			}
		}
	} else if progressionBody.Type == string(progression.ProgressionTypeChallenge) {
		if trailContent.Challenge == nil {
			return errors.New(errors.Service, "challenge not found", errors.NotFound, nil)
		}

		userProgression.UpdateChallengeStatus(progressionBody, trailContent.Challenge)
		userProgression.UpdateChallengeTotal(trailContent)
		userProgression.UpdateTrailTotal(trailContent)

		challengeProgress := userProgression.GetChallengeProgression(trailContent.ID)
		log.Println("Challenge progress: ", challengeProgress)
		if challengeProgress != nil && challengeProgress.Completed && !challengeProgress.Rewarded {
			userVault.Coins += ChallengeCompletionReward
			updateVault = true
			challengeProgress.Rewarded = true
		}
	}

	trailProgress := userProgression.GetTrailProgression(trailContent.ID)
	if trailProgress != nil && trailProgress.Total == TrailCompletionThreshold {
		if !trailProgress.Rewarded {
			userVault.Coins += TrailCompletionReward
			trailProgress.Rewarded = true
			updateVault = true
		}
	}

	if updateVault {
		if err = s.VaultService.Update(ctx, userVault); err != nil {
			return err
		}
	}

	// Update progression first
	if err = s.Update(ctx, userProgression); err != nil {
		return err
	}

	// Check for achievements after successful progression update
	if s.GamificationService != nil {
		if err := s.GamificationService.CheckAchievements(ctx, userId); err != nil {
			// Log error but don't fail the progression update
			log.Printf("Failed to check achievements for user %s: %v", userId, err)
		}
	}

	return nil
}

// Cache Helper

func (s *service) invalidateUserProgressionCache(ctx context.Context, userId string) {
	cacheKey := fmt.Sprintf("progression:user:%s", userId)
	err := s.Cache.Delete(ctx, cacheKey)
	if err != nil {
		log.Printf("Error invalidating user progression cache for user ID %s: %v", userId, err)
	} else {
		log.Printf("User progression cache invalidated for user ID: %s", userId)
	}
}
