package billing

import (
	"time"

	"github.com/imdario/mergo"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	DISABLED      Status = 0
	ACTIVE        Status = 1
	CANCELLED     Status = 2
	NOT_COMPLETED Status = 3
	ArkcBeginner         = "62a599a39057732b767ab5ac"
)

type Status uint8

type ContractCard struct {
	ID            string       `json:"id" bson:"-"`
	Status        Status       `json:"status" bson:"status"`
	Plan          *Product     `json:"plan,omitempty" bson:"plan"`
	PaymentData   *PaymentData `json:"paymentData,omitempty" bson:"paymentData"`
	LatestInvoice *Invoice     `json:"latestInvoice,omitempty" bson:"latestInvoice"`
}

type Contract struct {
	ObjectID         primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID               string             `json:"id,omitempty" bson:"-"`
	Status           Status             `json:"status" bson:"status"`
	Customer         string             `json:"customer" bson:"customer"`
	Plan             string             `json:"plan" bson:"plan"`
	PaymentData      *PaymentData       `json:"paymentData" bson:"paymentData"`
	LatestInvoice    string             `json:"LatestInvoice" bson:"latestInvoice"`
	ExternalCode     string             `json:"externalCode" bson:"externalCode"`
	CancellationDate time.Time          `json:"cancellationDate,omitempty" bson:"cancellationDate,omitempty"`
	CreatedAt        time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt        time.Time          `json:"updatedAt" bson:"updatedAt"`
}

type PaymentData struct {
	PaymentDate      time.Time `json:"paymentDate" bson:"paymentDate"`
	AutomaticRenewal bool      `json:"automaticRenewal" bson:"automaticRenewal"`
	ExpirationDate   time.Time `json:"expirationDate" bson:"expirationDate"`
}

func (c *Contract) ToCard(plan *Product, invoice *Invoice) *ContractCard {
	return &ContractCard{
		ID:            c.ID,
		Status:        c.Status,
		Plan:          plan,
		PaymentData:   c.PaymentData,
		LatestInvoice: invoice,
	}
}

func (c *Contract) PrepareCreate() error {

	c.CreatedAt = time.Now()
	c.UpdatedAt = c.CreatedAt

	if err := c.ValidateCreate(); err != nil {
		return err
	}

	if c.PaymentData != nil {
		if err := c.PaymentData.PrepareCreate(); err != nil {
			return err
		}
	}

	return nil
}

func (c *Contract) ValidateCreate() error {
	if c.Customer == "" {
		return ErrContractRequiredCustomer
	}

	if c.Plan == "" {
		return ErrContractRequiredPlan
	}

	if c.PaymentData == nil && c.Plan != ArkcBeginner {
		return ErrContractRequiredPaymentData
	}

	if c.Status != ACTIVE && c.Status != CANCELLED && c.Status != DISABLED && c.Status != NOT_COMPLETED {
		return ErrContractInvalidStatus
	}
	return nil
}

func (c *Contract) PrepareUpdate(newContract *Contract) error {
	if err := mergo.Merge(c, newContract, mergo.WithOverride); err != nil {
		return err
	}

	c.UpdatedAt = time.Now()

	if err := c.ValidateUpdate(); err != nil {
		return err
	}

	if err := c.PaymentData.PrepareUpdate(newContract.PaymentData); err != nil {
		return err
	}

	return nil
}

func (c *Contract) ValidateUpdate() error {
	return c.ValidateCreate()
}

func (p *PaymentData) PrepareCreate() error {
	return p.ValidateCreate()
}

func (p *PaymentData) ValidateCreate() error {
	if p.PaymentDate.IsZero() {
		return ErrPaymentDataRequiredPaymentDate
	}

	if !p.ExpirationDate.IsZero() {
		if p.PaymentDate.After(p.ExpirationDate) {
			return ErrPaymentDateMustBeBeforeExpirationDate
		}
	}

	return nil
}

func (p *PaymentData) PrepareUpdate(newPayment *PaymentData) error {
	if err := mergo.Merge(p, newPayment, mergo.WithOverride); err != nil {
		return err
	}

	return p.ValidateUpdate()
}

func (p *PaymentData) ValidateUpdate() error {
	return p.ValidateCreate()
}
