package progression

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Global map to store trail metadata for error reporting
var trailMetadata = make(map[string]*TrailWithMetadata)

// TestTrailMigrationFiles tests all trail files in the migration/trails directory
// to ensure they have valid paths and can be executed correctly
// This is a validation test, not a functional test, so we don't expect it to pass
// We're just checking that the trail files have the expected structure
func TestTrailMigrationFiles(t *testing.T) {
	// Create mocks
	mockRepo := new(MockProgressionRepo)
	mockTrailSvc := new(MockTrailSvc)
	mockVaultSvc := new(MockVaultSvc)
	mockContractSvc := new(MockContractSvc)
	mockCacheSvc := new(MockCacheSvc)

	// Create service
	service := New(
		mockRepo,
		mockTrailSvc,
		mockVaultSvc,
		mockContractSvc,
		mockCacheSvc,
		nil,
	)

	// Create test data
	ctx := context.Background()
	userId := "test-user-id"

	// Create mock vault
	mockVault := &model.Vault{
		ObjectID:  primitive.NewObjectID(),
		User:      userId,
		Coins:     0,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Create mock progression
	mockProgression := &progression.Progression{
		ObjectID:  primitive.NewObjectID(),
		User:      userId,
		Trails:    []*progression.Trail{},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Setup mocks
	mockVaultSvc.On("FindByUser", ctx, userId).Return(mockVault, nil)

	// Cache expectations - simulate cache miss for FindByUser
	mockCacheSvc.On("Get", mock.Anything, mock.Anything).Return(nil, false)
	mockCacheSvc.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
	// Cache invalidation on update
	mockCacheSvc.On("Delete", mock.Anything, mock.Anything).Return(nil)

	mockRepo.On("FindByUser", ctx, userId).Return(mockProgression, nil)
	mockVaultSvc.On("Update", ctx, mock.Anything).Return(nil)
	mockRepo.On("Update", ctx, mock.Anything).Return(nil)

	// Setup a default mock for trail requirements
	// This ensures that when we test trails with requirements, we have a valid progression
	// with the required trails already completed
	mockRepo.On("FindByUser", mock.Anything, mock.Anything).Return(mockProgression, nil)

	// Special handling for trail 67fd640f554f6eea40023cc3 which has specific requirements
	// This trail requires other trails to be completed first
	specialTrailId := "67fd640f554f6eea40023cc3"
	specialMockProgression := &progression.Progression{
		ObjectID:  primitive.NewObjectID(),
		User:      userId,
		Trails:    []*progression.Trail{},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Add required trails with 90% completion
	requiredTrailIds := []string{
		"67f6ddf3181babca8896e73c", // Início da Jornada
		"67fd63f5886e2c50f312b910", // Diagnosticar
		"67fd63fe286e566993c369f3", // Sistematizar
		"67fd6407fa56fc1ddbf88602", // Orçar
	}

	for _, reqTrailId := range requiredTrailIds {
		specialMockProgression.Trails = append(specialMockProgression.Trails, &progression.Trail{
			ID:        reqTrailId,
			Total:     90, // 90% completion is required for trail requirements
			Available: true,
		})
	}

	// Setup special mock for this trail
	mockRepo.On("FindByUser", mock.Anything, specialTrailId).Return(specialMockProgression, nil)

	// Find all trail files in the migration/trails directory
	// Try different paths to find the directory
	paths := []string{
		"migration/trails",
		"../../migration/trails",
		"../../../migration/trails",
	}

	var trailFiles []string
	var err error
	for _, path := range paths {
		trailFiles, err = findTrailFiles(path)
		if err == nil && len(trailFiles) > 0 {
			t.Logf("Found trail files in %s", path)
			break
		}
	}

	if err != nil {
		t.Fatalf("Failed to find trail files: %v", err)
	}

	if len(trailFiles) == 0 {
		t.Skip("No trail files found in migration/trails directory")
	}

	t.Logf("Found %d trail files", len(trailFiles))

	// Track issues found
	var issuesFound []string

	// Test each trail file
	for _, trailFile := range trailFiles {
		issues := testTrailFile(t, ctx, service, mockTrailSvc, mockRepo, userId, trailFile)
		issuesFound = append(issuesFound, issues...)
	}

	// Report issues found
	if len(issuesFound) > 0 {
		t.Logf("\n\nIssues found in trail files:")
		for _, issue := range issuesFound {
			t.Logf("- %s", issue)
		}
		t.Errorf("Test failed due to %d issues found in trail files", len(issuesFound))
	} else {
		t.Logf("\n\nNo issues found in trail files!")
	}
}

// findTrailFiles finds all JSON files in the specified directory and its subdirectories
func findTrailFiles(rootDir string) ([]string, error) {
	var files []string

	err := filepath.Walk(rootDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() && filepath.Ext(path) == ".json" {
			files = append(files, path)
		}
		return nil
	})

	return files, err
}

// testTrailFile tests a single trail file and returns a list of issues found
func testTrailFile(t *testing.T, ctx context.Context, service Service,
	mockTrailSvc *MockTrailSvc, mockRepo *MockProgressionRepo, userId string, trailFile string) []string {
	var issues []string
	fileName := filepath.Base(trailFile)

	// Check if this trail has requirements and add them to the user's progression
	if strings.Contains(fileName, "67fd640f554f6eea40023cc3") {
		// This trail requires the Sonhar trail (67fd63fe286e566993c369f3)
		addRequiredTrailToProgression(ctx, mockRepo, userId, "67fd63fe286e566993c369f3")
	}

	t.Run(fmt.Sprintf("Trail: %s", fileName), func(t *testing.T) {
		// Read and parse the trail file
		trail, err := readTrailFile(trailFile)
		if err != nil {
			issues = append(issues, fmt.Sprintf("Failed to read trail file %s: %v", fileName, err))
			t.Fatalf("Failed to read trail file %s: %v", trailFile, err)
		}

		// Check if we need to use ObjectID instead of ID
		if trail.ID == "" && !trail.ObjectID.IsZero() {
			trail.ID = trail.ObjectID.Hex()
			t.Logf("Using ObjectID %s as ID", trail.ID)
		}

		// If ID is still empty, use the filename without extension as ID
		if trail.ID == "" {
			fileName := filepath.Base(trailFile)
			extension := filepath.Ext(fileName)
			fileID := strings.TrimSuffix(fileName, extension)
			trail.ID = fileID
			t.Logf("Using filename %s as ID", trail.ID)

			// Check if the filename is a valid ObjectID
			isValidObjectID := len(fileID) == 24 && isHexString(fileID)
			if !isValidObjectID {
				issues = append(issues, fmt.Sprintf("Trail %s (%s) has invalid filename as ID: %s (should be a valid ObjectID)",
					trail.Name, trail.Identifier, fileID))
			}
		}

		// Skip if trail ID is still empty
		if trail.ID == "" {
			t.Skip("Trail ID is empty, ObjectID is zero, and filename is invalid, skipping")
			return
		}

		// Setup mock for this trail
		mockTrailSvc.On("Find", ctx, trail.ID).Return(trail, nil)

		// Setup mock trail requirements
		// For each trail requirement, create a mock trail progression with 90% completion
		if len(trail.Requirements) > 0 {
			t.Logf("Trail has %d requirements", len(trail.Requirements))
			for _, reqTrailId := range trail.Requirements {
				if reqTrailId == "" {
					continue // Skip empty requirements
				}

				// Create a mock trail for the requirement
				mockReqTrail := &content.Trail{
					ID:         reqTrailId,
					Name:       "Required Trail " + reqTrailId,
					Identifier: "req-trail-" + reqTrailId,
				}
				mockTrailSvc.On("Find", ctx, reqTrailId).Return(mockReqTrail, nil)

				// Add this required trail to the user's progression with 90% completion
				addRequiredTrailToProgression(ctx, mockRepo, userId, reqTrailId)
			}
		}

		t.Logf("Testing trail: %s (%s)", trail.Name, trail.Identifier)
		t.Logf("Number of lessons: %d", len(trail.Lessons))
		if trail.Challenge != nil {
			t.Logf("Challenge: %s (%s)", trail.Challenge.Name, trail.Challenge.Identifier)
			t.Logf("Number of phases: %d", len(trail.Challenge.Phases))
		}

		// Check for content items with both choices and next fields
		for identifier := range trailMetadata[trailFile].ContentWithNextField {
			lineNum := getContentLineNumberFromMetadata(trail.ID, identifier)
			lineInfo := ""
			if lineNum > 0 {
				lineInfo = fmt.Sprintf(" (line %d)", lineNum)
			}

			// Find the lesson or phase that contains this content item
			lessonOrPhase := ""
			lessonOrPhaseType := ""
			for _, lesson := range trail.Lessons {
				for _, content := range lesson.Content {
					if content.Identifier == identifier {
						lessonOrPhase = lesson.Identifier
						lessonOrPhaseType = "Lesson"
						break
					}
				}
			}

			if lessonOrPhase == "" && trail.Challenge != nil {
				for _, phase := range trail.Challenge.Phases {
					for _, content := range phase.Content {
						if content.Identifier == identifier {
							lessonOrPhase = phase.Identifier
							lessonOrPhaseType = "Phase"
							break
						}
					}
				}
			}

			if lessonOrPhase != "" {
				issues = append(issues, fmt.Sprintf("Trail %s, %s %s, Content %s has both choices and an empty next field%s",
					trail.ID, lessonOrPhaseType, lessonOrPhase, identifier, lineInfo))
			}
		}

		// Check for unreachable content items
		unreachableIssues := checkUnreachableContent(t, trail)
		issues = append(issues, unreachableIssues...)

		// Test each lesson in the trail
		for _, lesson := range trail.Lessons {
			lessonIssues := testLesson(t, ctx, service, mockRepo, userId, trail.ID, lesson)
			issues = append(issues, lessonIssues...)
		}

		// Test the challenge if it exists
		if trail.Challenge != nil && len(trail.Challenge.Phases) > 0 {
			challengeIssues := testChallenge(t, ctx, service, mockRepo, userId, trail)
			issues = append(issues, challengeIssues...)
		}

		// Fail the subtest if issues were found
		if len(issues) > 0 {
			t.Errorf("Trail %s has %d issues", trail.ID, len(issues))
		}
	})

	return issues
}

// TrailWithMetadata extends content.Trail with metadata for testing
type TrailWithMetadata struct {
	*content.Trail
	FilePath             string
	LineInfo             map[string]int  // Maps content identifiers to line numbers
	ContentWithNextField map[string]bool // Maps content identifiers to whether they have a next field in the JSON
}

// getContentLineNumberFromMetadata returns the line number for a content identifier
func getContentLineNumberFromMetadata(trailId string, identifier string) int {
	// Find the trail file path from the trail ID
	for _, metadata := range trailMetadata {
		if metadata.Trail.ID == trailId {
			if lineNum, ok := metadata.LineInfo[identifier]; ok {
				return lineNum
			}
			break
		}
	}
	return 0 // Line number not found
}

// hasNextFieldInJSON checks if a content item has a next field in the JSON
func hasNextFieldInJSON(trailId string, identifier string) bool {
	// Find the trail file path from the trail ID
	for _, metadata := range trailMetadata {
		if metadata.Trail.ID == trailId {
			// Check if the content has a next field in the JSON
			if metadata.ContentWithNextField != nil {
				if _, ok := metadata.ContentWithNextField[identifier]; ok {
					return true
				}
			}
			break
		}
	}
	return false // Next field not found
}

// readTrailFile reads and parses a trail file
func readTrailFile(filePath string) (*content.Trail, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	var trail content.Trail
	err = json.Unmarshal(data, &trail)
	if err != nil {
		return nil, err
	}

	// Store metadata in a global map for reference in error messages
	lineInfo, contentWithNextField := findContentMetadata(string(data))
	trailMetadata[filePath] = &TrailWithMetadata{
		Trail:                &trail,
		FilePath:             filePath,
		LineInfo:             lineInfo,
		ContentWithNextField: contentWithNextField,
	}

	return &trail, nil
}

// testLesson tests a single lesson by simulating user progression through it
func testLesson(t *testing.T, ctx context.Context, service Service, mockRepo *MockProgressionRepo, userId string, trailId string, lesson *content.Lesson) []string {
	var issues []string
	t.Logf("Testing lesson: %s (%s)", lesson.Name, lesson.Identifier)

	// Skip if lesson identifier is empty
	if lesson.Identifier == "" {
		t.Logf("Lesson identifier is empty, skipping")
		issues = append(issues, fmt.Sprintf("Trail %s has lesson with empty identifier", trailId))
		return issues
	}

	if len(lesson.Content) == 0 {
		t.Logf("Lesson has no content, skipping")
		issues = append(issues, fmt.Sprintf("Trail %s, Lesson %s has no content", trailId, lesson.Identifier))
		return issues
	}

	// Check if lesson has requirements
	if len(lesson.Requirements) > 0 {
		t.Logf("Lesson has %d requirements", len(lesson.Requirements))
		// For each lesson requirement, add it to the user's progression as completed
		for _, reqLessonId := range lesson.Requirements {
			if reqLessonId == "" {
				continue // Skip empty requirements
			}
			// Add this required lesson to the user's progression as completed
			addRequiredLessonToProgression(ctx, mockRepo, userId, trailId, reqLessonId)
		}
	} else {
		// If the lesson has no requirements, it's the first lesson in the trail
		// We need to ensure the trail is available to the user
		addTrailToUserProgression(ctx, mockRepo, userId, trailId)
	}

	t.Logf("Lesson has %d content items", len(lesson.Content))

	// Track the path through the lesson
	var path []*content.LessonContent
	currentContent := lesson.Content[0]
	path = append(path, currentContent)

	// Follow the path through the lesson
	for {
		t.Logf("Content: %s, Next: %s", currentContent.Identifier, currentContent.Next)

		// Skip if content identifier is empty
		if currentContent.Identifier == "" {
			t.Logf("Content identifier is empty, skipping")
			break
		}

		// Check if content has choices
		hasChoices := len(currentContent.Choices) > 0

		// Get the next value
		nextValue := currentContent.Next

		// If content has choices
		if hasChoices {
			// Content with choices should NOT have a next value at the top level
			// Even if the next value is empty, the field should not be present at all
			// But since we're accessing it through the struct, we can only check if it's empty or not
			// The JSON parser will set it to "" if it's present in the JSON
			// So we'll check if the next field is present in the JSON
			// Get line number for the content
			lineNum := getContentLineNumberFromMetadata(trailId, currentContent.Identifier)
			lineInfo := ""
			if lineNum > 0 {
				lineInfo = fmt.Sprintf(" (line %d)", lineNum)
			}

			// Check if the next field is present in the JSON
			// We need to use a custom function to check this
			hasNextField := hasNextFieldInJSON(trailId, currentContent.Identifier)
			if hasNextField {
				if currentContent.Next != "" {
					t.Logf("Warning: Content %s has both choices and a non-empty next field%s", currentContent.Identifier, lineInfo)
					issues = append(issues, fmt.Sprintf("Trail %s, Lesson %s, Content %s has both choices and a non-empty next field%s",
						trailId, lesson.Identifier, currentContent.Identifier, lineInfo))
				} else {
					t.Logf("Warning: Content %s has both choices and an empty next field%s", currentContent.Identifier, lineInfo)
					issues = append(issues, fmt.Sprintf("Trail %s, Lesson %s, Content %s has both choices and an empty next field%s",
						trailId, lesson.Identifier, currentContent.Identifier, lineInfo))
				}
			}

			// Use the next value from the first choice
			if len(currentContent.Choices) > 0 && currentContent.Choices[0].Next != "" {
				nextValue = currentContent.Choices[0].Next
				// Update currentContent.Next to match the first choice's next value
				// This is needed for the test to correctly follow the path through the lesson
				currentContent.Next = nextValue
				t.Logf("Content %s has choices, using first choice's next: %s", currentContent.Identifier, nextValue)
			} else {
				// If no valid next in choices, use coin as fallback
				nextValue = "coin"
				// Update currentContent.Next to match the fallback value
				currentContent.Next = nextValue
				t.Logf("Content %s has choices but no valid next values, using 'coin'", currentContent.Identifier)
				issues = append(issues, fmt.Sprintf("Trail %s, Lesson %s, Content %s has choices but no valid next values", trailId, lesson.Identifier, currentContent.Identifier))
			}
		} else if nextValue == "" {
			// No choices and no next value is an error
			nextValue = "coin"
			t.Logf("Setting empty Next to 'coin' for content %s", currentContent.Identifier)
			issues = append(issues, fmt.Sprintf("Trail %s, Lesson %s, Content %s is missing Next value", trailId, lesson.Identifier, currentContent.Identifier))
		}

		// Create progression body
		progressionBody := &progression.ProgressionBody{
			Trail:   trailId,
			Module:  lesson.Identifier,
			Content: currentContent.Identifier,
			Type:    string(progression.ProgressionTypeLesson),
			Choice: &progression.ModuleContentChoice{
				Identifier: currentContent.Identifier,
				Next:       nextValue,
			},
		}

		// If the content has choices, we need to set the choice identifier to the first choice
		// This simulates the user selecting the first choice
		if hasChoices && len(currentContent.Choices) > 0 {
			progressionBody.Choice.Identifier = currentContent.Choices[0].Identifier
		}

		// Try to register progression, but don't fail the test if it fails
		// We're just checking that the trail files have the expected structure
		err := service.CreateLesson(ctx, userId, "standard", []string{}, progressionBody)
		if err != nil {
			// If the content has choices, this is expected to fail
			if hasChoices {
				// This is expected, content with choices should not have a next field
				t.Logf("Expected error: Failed to register lesson progression for content with choices: %v", err)
			} else {
				// This is unexpected, content without choices should have a next field
				t.Errorf("Error: Failed to register lesson progression: %v", err)
				issues = append(issues, fmt.Sprintf("Trail %s, Lesson %s, Content %s failed to register progression: %v",
					trailId, lesson.Identifier, currentContent.Identifier, err))
			}
		}

		// Check if we've reached the end of the lesson
		if currentContent.Next == "coin" {
			t.Logf("Reached end of lesson with reward: %s", currentContent.Next)
			break
		}

		// Find the next content
		found := false
		for _, content := range lesson.Content {
			if content.Identifier == currentContent.Next {
				currentContent = content
				path = append(path, currentContent)
				found = true
				break
			}
		}

		if !found {
			// If next is empty or we've set a default value, this is expected
			if currentContent.Next == "" || currentContent.Next == "coin" {
				t.Logf("Reached end of lesson with next: %s", currentContent.Next)
				break
			}
			t.Errorf("Invalid path: next content '%s' not found in lesson", currentContent.Next)
			break
		}

		// Prevent infinite loops
		if len(path) > 100 {
			t.Errorf("Possible infinite loop detected in lesson path")
			break
		}
	}

	// Verify the path is valid
	assert.Greater(t, len(path), 0, "Lesson should have at least one content item")

	// Check for cycles and unreachable rewards using graph analysis
	// Build a graph of all content items
	graph := buildLessonGraph(lesson.Content)

	// Check for cycles
	cycles := graph.DetectCycles()
	for _, cycle := range cycles {
		t.Logf("Warning: Cycle detected in lesson starting at content %s", cycle)
		issues = append(issues, fmt.Sprintf("Trail %s, Lesson %s has a cycle starting at content %s",
			trailId, lesson.Identifier, cycle))
	}

	// Check for unreachable rewards
	unreachable := graph.CheckReachability()
	for _, contentId := range unreachable {
		t.Logf("Warning: Content %s cannot reach a reward", contentId)
		issues = append(issues, fmt.Sprintf("Trail %s, Lesson %s, Content %s cannot reach a reward",
			trailId, lesson.Identifier, contentId))
	}

	return issues
}

// testChallenge tests a challenge by simulating user progression through it
func testChallenge(t *testing.T, ctx context.Context, service Service, mockRepo *MockProgressionRepo, userId string, trail *content.Trail) []string {
	var issues []string
	challenge := trail.Challenge
	t.Logf("Testing challenge: %s (%s)", challenge.Name, challenge.Identifier)

	// Skip if challenge identifier is empty
	if challenge.Identifier == "" {
		t.Logf("Challenge identifier is empty, skipping")
		issues = append(issues, fmt.Sprintf("Trail %s has challenge with empty identifier", trail.ID))
		return issues
	}

	// Mark all lessons as completed to satisfy the challenge requirement
	// This simulates that the user has completed all lessons in the trail
	markAllLessonsCompleted(ctx, mockRepo, userId, trail)

	// Test each phase in the challenge
	for _, phase := range challenge.Phases {
		phaseIssues := testChallengePhase(t, ctx, service, mockRepo, userId, trail.ID, phase)
		issues = append(issues, phaseIssues...)
	}

	return issues
}

// testChallengePhase tests a single challenge phase
func testChallengePhase(t *testing.T, ctx context.Context, service Service, mockRepo *MockProgressionRepo, userId string, trailId string, phase *content.ChallengePhase) []string {
	var issues []string
	t.Logf("Testing challenge phase: %s (%s)", phase.Name, phase.Identifier)

	// Skip if phase identifier is empty
	if phase.Identifier == "" {
		t.Logf("Phase identifier is empty, skipping")
		issues = append(issues, fmt.Sprintf("Trail %s has challenge phase with empty identifier", trailId))
		return issues
	}

	if len(phase.Content) == 0 {
		t.Logf("Phase has no content, skipping")
		issues = append(issues, fmt.Sprintf("Trail %s, Phase %s has no content", trailId, phase.Identifier))
		return issues
	}

	// Check if phase has requirements
	if len(phase.Requirements) > 0 {
		t.Logf("Phase has %d requirements", len(phase.Requirements))
		// For each phase requirement, add it to the user's progression as completed
		for _, reqPhaseId := range phase.Requirements {
			if reqPhaseId == "" {
				continue // Skip empty requirements
			}
			// Add this required phase to the user's progression as completed
			addRequiredPhaseToProgression(ctx, mockRepo, userId, trailId, reqPhaseId)
		}
	}

	t.Logf("Phase has %d content items", len(phase.Content))

	// Check for challenge choices with missing or invalid types
	for _, content := range phase.Content {
		if len(content.Choices) > 0 {
			for _, choice := range content.Choices {
				choiceIssues := validateChallengeChoiceType(t, trailId, phase.Identifier, content.Identifier, choice)
				issues = append(issues, choiceIssues...)
			}
		}
	}

	// Track the path through the phase
	var path []*content.ChallengeContent
	currentContent := phase.Content[0]
	path = append(path, currentContent)

	// Follow the path through the phase
	for {
		t.Logf("Content: %s, Next: %s", currentContent.Identifier, currentContent.Next)

		// Skip if content identifier is empty
		if currentContent.Identifier == "" {
			t.Logf("Content identifier is empty, skipping")
			break
		}

		// Check if content has choices
		hasChoices := len(currentContent.Choices) > 0

		// Get the next value
		nextValue := currentContent.Next

		// If content has choices
		if hasChoices {
			// Content with choices should NOT have a next value at the top level
			// Even if the next value is empty, the field should not be present at all
			// But since we're accessing it through the struct, we can only check if it's empty or not
			// The JSON parser will set it to "" if it's present in the JSON
			// So we'll check if the next field is present in the JSON
			// Get line number for the content
			lineNum := getContentLineNumberFromMetadata(trailId, currentContent.Identifier)
			lineInfo := ""
			if lineNum > 0 {
				lineInfo = fmt.Sprintf(" (line %d)", lineNum)
			}

			// Check if the next field is present in the JSON
			// We need to use a custom function to check this
			hasNextField := hasNextFieldInJSON(trailId, currentContent.Identifier)
			if hasNextField {
				if currentContent.Next != "" {
					t.Logf("Warning: Content %s has both choices and a non-empty next field%s", currentContent.Identifier, lineInfo)
					issues = append(issues, fmt.Sprintf("Trail %s, Phase %s, Content %s has both choices and a non-empty next field%s",
						trailId, phase.Identifier, currentContent.Identifier, lineInfo))
				} else {
					t.Logf("Warning: Content %s has both choices and an empty next field%s", currentContent.Identifier, lineInfo)
					issues = append(issues, fmt.Sprintf("Trail %s, Phase %s, Content %s has both choices and an empty next field%s",
						trailId, phase.Identifier, currentContent.Identifier, lineInfo))
				}
			}

			// Use the next value from the first choice
			if len(currentContent.Choices) > 0 && currentContent.Choices[0].Next != "" {
				nextValue = currentContent.Choices[0].Next
				// Update currentContent.Next to match the first choice's next value
				// This is needed for the test to correctly follow the path through the phase
				currentContent.Next = nextValue
				t.Logf("Content %s has choices, using first choice's next: %s", currentContent.Identifier, nextValue)
			} else {
				// If no valid next in choices, use coin as fallback
				nextValue = "coin"
				// Update currentContent.Next to match the fallback value
				currentContent.Next = nextValue
				t.Logf("Content %s has choices but no valid next values, using 'coin'", currentContent.Identifier)
				issues = append(issues, fmt.Sprintf("Trail %s, Phase %s, Content %s has choices but no valid next values", trailId, phase.Identifier, currentContent.Identifier))
			}
		} else if nextValue == "" {
			// No choices and no next value is an error
			nextValue = "coin"
			t.Logf("Setting empty Next to 'coin' for content %s", currentContent.Identifier)
			issues = append(issues, fmt.Sprintf("Trail %s, Phase %s, Content %s is missing Next value", trailId, phase.Identifier, currentContent.Identifier))
		}

		// Create progression body
		progressionBody := &progression.ProgressionBody{
			Trail:   trailId,
			Module:  phase.Identifier,
			Content: currentContent.Identifier,
			Type:    string(progression.ProgressionTypeChallenge),
			Choice: &progression.ModuleContentChoice{
				Identifier: currentContent.Identifier,
				Next:       nextValue,
			},
		}

		// If the content has choices, we need to set the choice identifier to the first choice
		// This simulates the user selecting the first choice
		if hasChoices && len(currentContent.Choices) > 0 {
			progressionBody.Choice.Identifier = currentContent.Choices[0].Identifier
		}

		// Try to register progression, but don't fail the test if it fails
		// We're just checking that the trail files have the expected structure
		err := service.CreateChallenge(ctx, userId, "standard", []string{}, progressionBody)
		if err != nil {
			// If the content has choices, this is expected to fail
			if hasChoices {
				// This is expected, content with choices should not have a next field
				t.Logf("Expected error: Failed to register challenge progression for content with choices: %v", err)
			} else {
				// This is unexpected, content without choices should have a next field
				t.Errorf("Error: Failed to register challenge progression: %v", err)
				issues = append(issues, fmt.Sprintf("Trail %s, Phase %s, Content %s failed to register progression: %v",
					trailId, phase.Identifier, currentContent.Identifier, err))
			}
		}

		// Check if we've reached the end of the phase
		if currentContent.Next == "coin" || currentContent.Next == "free" {
			t.Logf("Reached end of phase with reward: %s", currentContent.Next)
			break
		}

		// Find the next content
		found := false
		for _, content := range phase.Content {
			if content.Identifier == currentContent.Next {
				currentContent = content
				path = append(path, currentContent)
				found = true
				break
			}
		}

		if !found {
			// If next is empty or we've set a default value, this is expected
			if currentContent.Next == "" || currentContent.Next == "coin" {
				t.Logf("Reached end of phase with next: %s", currentContent.Next)
				break
			}
			t.Errorf("Invalid path: next content '%s' not found in phase", currentContent.Next)
			break
		}

		// Prevent infinite loops
		if len(path) > 100 {
			t.Errorf("Possible infinite loop detected in phase path")
			break
		}
	}

	// Verify the path is valid
	assert.Greater(t, len(path), 0, "Phase should have at least one content item")

	// Check for cycles and unreachable rewards using graph analysis
	// Build a graph of all content items
	graph := buildChallengeGraph(phase.Content)

	// Check for cycles
	cycles := graph.DetectCycles()
	for _, cycle := range cycles {
		t.Logf("Warning: Cycle detected in phase starting at content %s", cycle)
		issues = append(issues, fmt.Sprintf("Trail %s, Phase %s has a cycle starting at content %s",
			trailId, phase.Identifier, cycle))
	}

	// Check for unreachable rewards
	unreachable := graph.CheckReachability()
	for _, contentId := range unreachable {
		t.Logf("Warning: Content %s cannot reach a reward", contentId)
		issues = append(issues, fmt.Sprintf("Trail %s, Phase %s, Content %s cannot reach a reward",
			trailId, phase.Identifier, contentId))
	}

	return issues
}

// isHexString checks if a string contains only hexadecimal characters
func isHexString(s string) bool {
	for _, c := range s {
		if !((c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F')) {
			return false
		}
	}
	return true
}

// Helper methods for handling requirements in tests

// Helper functions for test requirements
// These are standalone functions that don't depend on the service implementation

// addTrailToUserProgression adds a trail to the user's progression and makes it available
func addTrailToUserProgression(ctx context.Context, mockRepo *MockProgressionRepo, userId string, trailId string) {
	// Get the existing mock progression
	mockProgression, _ := mockRepo.FindByUser(ctx, userId)
	if mockProgression == nil {
		// Create a new progression if it doesn't exist
		mockProgression = &progression.Progression{
			ObjectID:  primitive.NewObjectID(),
			User:      userId,
			Trails:    []*progression.Trail{},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
	}

	// Check if the trail already exists
	trailExists := false
	for _, trail := range mockProgression.Trails {
		if trail.ID == trailId {
			// Update the existing trail
			trail.Available = true
			trailExists = true
			break
		}
	}

	// Add the trail if it doesn't exist
	if !trailExists {
		mockProgression.Trails = append(mockProgression.Trails, &progression.Trail{
			ID:        trailId,
			Available: true,
		})
	}

	// Setup the mock to return this progression
	mockRepo.On("FindByUser", ctx, userId).Return(mockProgression, nil).Once()
}

// addRequiredTrailToProgression adds a required trail to the user's progression with 90% completion
func addRequiredTrailToProgression(ctx context.Context, mockRepo *MockProgressionRepo, userId string, trailId string) {
	// Get the existing mock progression
	mockProgression, _ := mockRepo.FindByUser(ctx, userId)
	if mockProgression == nil {
		// Create a new progression if it doesn't exist
		mockProgression = &progression.Progression{
			ObjectID:  primitive.NewObjectID(),
			User:      userId,
			Trails:    []*progression.Trail{},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
	}

	// Check if the trail already exists
	trailExists := false
	for _, trail := range mockProgression.Trails {
		if trail.ID == trailId {
			// Update the existing trail
			trail.Total = 90 // 90% completion is required for trail requirements
			trail.Available = true
			trail.LessonsCompleted = true // Mark lessons as completed for challenge requirements
			trailExists = true
			break
		}
	}

	// Add the trail if it doesn't exist
	if !trailExists {
		mockProgression.Trails = append(mockProgression.Trails, &progression.Trail{
			ID:               trailId,
			Total:            90, // 90% completion is required for trail requirements
			Available:        true,
			LessonsCompleted: true,                    // Mark lessons as completed for challenge requirements
			Lessons:          []*progression.Lesson{}, // Initialize lessons array
		})
	}

	// Special handling for specific trails
	if trailId == "67fd63fe286e566993c369f3" { // Sonhar trail
		// Add required trail for Sonhar
		addRequiredTrailToProgression(ctx, mockRepo, userId, "67fd63f5886e2c50f312b910") // Diagnosticar
	} else if trailId == "67fd63f5886e2c50f312b910" { // Diagnosticar trail
		// Add required trail for Diagnosticar
		addRequiredTrailToProgression(ctx, mockRepo, userId, "67f6ddf3181babca8896e73c") // Início da Jornada
	}

	// Setup the mock to return this progression
	mockRepo.On("FindByUser", ctx, userId).Return(mockProgression, nil).Once()
}

// addRequiredLessonToProgression adds a required lesson to the user's progression as completed
func addRequiredLessonToProgression(ctx context.Context, mockRepo *MockProgressionRepo, userId string, trailId string, lessonId string) {
	// Get the existing mock progression
	mockProgression, _ := mockRepo.FindByUser(ctx, userId)
	if mockProgression == nil {
		// Create a new progression if it doesn't exist
		mockProgression = &progression.Progression{
			ObjectID:  primitive.NewObjectID(),
			User:      userId,
			Trails:    []*progression.Trail{},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
	}

	// Find or create the trail
	var trailProgression *progression.Trail
	for _, trail := range mockProgression.Trails {
		if trail.ID == trailId {
			trailProgression = trail
			break
		}
	}

	// Create the trail if it doesn't exist
	if trailProgression == nil {
		trailProgression = &progression.Trail{
			ID:        trailId,
			Available: true,
			Lessons:   []*progression.Lesson{},
		}
		mockProgression.Trails = append(mockProgression.Trails, trailProgression)
	}

	// Check if the lesson already exists
	lessonExists := false
	for _, lesson := range trailProgression.Lessons {
		if lesson.Identifier == lessonId {
			// Update the existing lesson
			lesson.Completed = true
			lesson.Available = true
			lesson.Rewarded = true
			lessonExists = true
			break
		}
	}

	// Add the lesson if it doesn't exist
	if !lessonExists {
		trailProgression.Lessons = append(trailProgression.Lessons, &progression.Lesson{
			Identifier: lessonId,
			Completed:  true,
			Available:  true,
			Rewarded:   true,
		})
	}

	// Setup the mock to return this progression
	mockRepo.On("FindByUser", ctx, userId).Return(mockProgression, nil).Once()
}

// markAllLessonsCompleted marks all lessons in a trail as completed
func markAllLessonsCompleted(ctx context.Context, mockRepo *MockProgressionRepo, userId string, trail *content.Trail) {
	// Get the existing mock progression
	mockProgression, _ := mockRepo.FindByUser(ctx, userId)
	if mockProgression == nil {
		// Create a new progression if it doesn't exist
		mockProgression = &progression.Progression{
			ObjectID:  primitive.NewObjectID(),
			User:      userId,
			Trails:    []*progression.Trail{},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
	}

	// Find or create the trail
	var trailProgression *progression.Trail
	for _, t := range mockProgression.Trails {
		if t.ID == trail.ID {
			trailProgression = t
			break
		}
	}

	// Create the trail if it doesn't exist
	if trailProgression == nil {
		trailProgression = &progression.Trail{
			ID:               trail.ID,
			Available:        true,
			LessonsCompleted: true,
			Lessons:          []*progression.Lesson{},
		}
		mockProgression.Trails = append(mockProgression.Trails, trailProgression)
	} else {
		// Mark lessons as completed
		trailProgression.LessonsCompleted = true
	}

	// Add all lessons as completed
	for _, lesson := range trail.Lessons {
		// Check if the lesson already exists
		lessonExists := false
		for _, l := range trailProgression.Lessons {
			if l.Identifier == lesson.Identifier {
				// Update the existing lesson
				l.Completed = true
				l.Available = true
				l.Rewarded = true
				lessonExists = true
				break
			}
		}

		// Add the lesson if it doesn't exist
		if !lessonExists {
			trailProgression.Lessons = append(trailProgression.Lessons, &progression.Lesson{
				Identifier: lesson.Identifier,
				Completed:  true,
				Available:  true,
				Rewarded:   true,
			})
		}
	}

	// Setup the mock to return this progression
	mockRepo.On("FindByUser", ctx, userId).Return(mockProgression, nil).Once()
}

// addRequiredPhaseToProgression adds a required challenge phase to the user's progression as completed
func addRequiredPhaseToProgression(ctx context.Context, mockRepo *MockProgressionRepo, userId string, trailId string, phaseId string) {
	// Get the existing mock progression
	mockProgression, _ := mockRepo.FindByUser(ctx, userId)
	if mockProgression == nil {
		// Create a new progression if it doesn't exist
		mockProgression = &progression.Progression{
			ObjectID:  primitive.NewObjectID(),
			User:      userId,
			Trails:    []*progression.Trail{},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
	}

	// Find or create the trail
	var trailProgression *progression.Trail
	for _, trail := range mockProgression.Trails {
		if trail.ID == trailId {
			trailProgression = trail
			break
		}
	}

	// Create the trail if it doesn't exist
	if trailProgression == nil {
		trailProgression = &progression.Trail{
			ID:               trailId,
			Available:        true,
			LessonsCompleted: true, // Challenges require lessons to be completed
		}
		mockProgression.Trails = append(mockProgression.Trails, trailProgression)
	} else {
		// Mark lessons as completed (required for challenges)
		trailProgression.LessonsCompleted = true
	}

	// Create the challenge if it doesn't exist
	if trailProgression.Challenge == nil {
		trailProgression.Challenge = &progression.Challenge{
			Available: true,
			Phases:    []*progression.ChallengePhase{},
		}
	}

	// Check if the phase already exists
	phaseExists := false
	for _, phase := range trailProgression.Challenge.Phases {
		if phase.Identifier == phaseId {
			// Update the existing phase
			phase.Completed = true
			phase.Available = true
			phase.Rewarded = true
			phaseExists = true
			break
		}
	}

	// Add the phase if it doesn't exist
	if !phaseExists {
		trailProgression.Challenge.Phases = append(trailProgression.Challenge.Phases, &progression.ChallengePhase{
			Identifier: phaseId,
			Completed:  true,
			Available:  true,
			Rewarded:   true,
		})
	}

	// Setup the mock to return this progression
	mockRepo.On("FindByUser", ctx, userId).Return(mockProgression, nil).Once()
}

// checkUnreachableContent checks for content items that are not reachable from any other content item
func checkUnreachableContent(t *testing.T, trail *content.Trail) []string {
	var issues []string

	// Check each lesson for unreachable content
	for _, lesson := range trail.Lessons {
		lessonIssues := checkUnreachableContentInLesson(t, trail.ID, lesson)
		issues = append(issues, lessonIssues...)
	}

	// Check each challenge phase for unreachable content
	if trail.Challenge != nil {
		for _, phase := range trail.Challenge.Phases {
			phaseIssues := checkUnreachableContentInPhase(t, trail.ID, phase)
			issues = append(issues, phaseIssues...)
		}
	}

	return issues
}

// checkUnreachableContentInLesson checks for content items that are not reachable in a lesson
func checkUnreachableContentInLesson(t *testing.T, trailId string, lesson *content.Lesson) []string {
	var issues []string

	// Skip if lesson has no content
	if len(lesson.Content) == 0 {
		return issues
	}

	// Build a map of all content items
	contentMap := make(map[string]*content.LessonContent)
	for _, content := range lesson.Content {
		contentMap[content.Identifier] = content
	}

	// Build a map of reachable content items
	reachable := make(map[string]bool)

	// The first content item is always reachable
	firstContent := lesson.Content[0]
	reachable[firstContent.Identifier] = true

	// Mark all content items that are reachable from the first content item
	markReachableContent(firstContent, contentMap, reachable)

	// Check for unreachable content items
	for _, content := range lesson.Content {
		if !reachable[content.Identifier] {
			// Get line number for the content
			lineNum := getContentLineNumberFromMetadata(trailId, content.Identifier)
			lineInfo := ""
			if lineNum > 0 {
				lineInfo = fmt.Sprintf(" (line %d)", lineNum)
			}

			t.Logf("Warning: Content %s is not reachable in lesson %s%s", content.Identifier, lesson.Identifier, lineInfo)
			issues = append(issues, fmt.Sprintf("Trail %s, Lesson %s, Content %s is not reachable%s",
				trailId, lesson.Identifier, content.Identifier, lineInfo))
		}
	}

	return issues
}

// checkUnreachableContentInPhase checks for content items that are not reachable in a challenge phase
func checkUnreachableContentInPhase(t *testing.T, trailId string, phase *content.ChallengePhase) []string {
	var issues []string

	// Skip if phase has no content
	if len(phase.Content) == 0 {
		return issues
	}

	// Build a map of all content items
	contentMap := make(map[string]*content.ChallengeContent)
	for _, content := range phase.Content {
		contentMap[content.Identifier] = content
	}

	// Build a map of reachable content items
	reachable := make(map[string]bool)

	// The first content item is always reachable
	firstContent := phase.Content[0]
	reachable[firstContent.Identifier] = true

	// Mark all content items that are reachable from the first content item
	markReachableChallengeContent(firstContent, contentMap, reachable)

	// Check for unreachable content items
	for _, content := range phase.Content {
		if !reachable[content.Identifier] {
			// Get line number for the content
			lineNum := getContentLineNumberFromMetadata(trailId, content.Identifier)
			lineInfo := ""
			if lineNum > 0 {
				lineInfo = fmt.Sprintf(" (line %d)", lineNum)
			}

			t.Logf("Warning: Content %s is not reachable in phase %s%s", content.Identifier, phase.Identifier, lineInfo)
			issues = append(issues, fmt.Sprintf("Trail %s, Phase %s, Content %s is not reachable%s",
				trailId, phase.Identifier, content.Identifier, lineInfo))
		}
	}

	return issues
}

// markReachableContent recursively marks all content items that are reachable from the given content item
func markReachableContent(content *content.LessonContent, contentMap map[string]*content.LessonContent, reachable map[string]bool) {
	// If content has a next value, mark it as reachable
	if content.Next != "" && content.Next != "coin" && content.Next != "free" {
		if nextContent, ok := contentMap[content.Next]; ok {
			if !reachable[content.Next] {
				reachable[content.Next] = true
				markReachableContent(nextContent, contentMap, reachable)
			}
		}
	}

	// If content has choices, mark all next values as reachable
	for _, choice := range content.Choices {
		if choice.Next != "" && choice.Next != "coin" && choice.Next != "free" {
			if nextContent, ok := contentMap[choice.Next]; ok {
				if !reachable[choice.Next] {
					reachable[choice.Next] = true
					markReachableContent(nextContent, contentMap, reachable)
				}
			}
		}
	}
}

// markReachableChallengeContent recursively marks all content items that are reachable from the given content item
func markReachableChallengeContent(content *content.ChallengeContent, contentMap map[string]*content.ChallengeContent, reachable map[string]bool) {
	// If content has a next value, mark it as reachable
	if content.Next != "" && content.Next != "coin" && content.Next != "free" {
		if nextContent, ok := contentMap[content.Next]; ok {
			if !reachable[content.Next] {
				reachable[content.Next] = true
				markReachableChallengeContent(nextContent, contentMap, reachable)
			}
		}
	}

	// If content has choices, mark all next values as reachable
	for _, choice := range content.Choices {
		if choice.Next != "" && choice.Next != "coin" && choice.Next != "free" {
			if nextContent, ok := contentMap[choice.Next]; ok {
				if !reachable[choice.Next] {
					reachable[choice.Next] = true
					markReachableChallengeContent(nextContent, contentMap, reachable)
				}
			}
		}
	}
}

// ContentGraph represents a directed graph of content items
type ContentGraph struct {
	nodes map[string]*ContentNode
}

// ContentNode represents a node in the content graph
type ContentNode struct {
	identifier     string
	next           []string // Can be multiple for choices
	visited        bool     // For cycle detection
	inPath         bool     // For cycle detection
	canReachReward bool     // For reachability analysis
}

// buildLessonGraph builds a graph from lesson content items
func buildLessonGraph(contents []*content.LessonContent) *ContentGraph {
	graph := &ContentGraph{
		nodes: make(map[string]*ContentNode),
	}

	// Add all nodes
	for _, content := range contents {
		node := &ContentNode{
			identifier: content.Identifier,
			next:       []string{},
		}

		// Add regular next
		if content.Next != "" {
			node.next = append(node.next, content.Next)
		}

		// Add choices
		for _, choice := range content.Choices {
			if choice.Next != "" {
				node.next = append(node.next, choice.Next)
			}
		}

		graph.nodes[content.Identifier] = node
	}

	// Add special reward nodes
	graph.nodes["coin"] = &ContentNode{
		identifier:     "coin",
		canReachReward: true, // Rewards can reach themselves
	}
	graph.nodes["free"] = &ContentNode{
		identifier:     "free",
		canReachReward: true, // "free" is also a valid endpoint
	}

	return graph
}

// buildChallengeGraph builds a graph from challenge content items
func buildChallengeGraph(contents []*content.ChallengeContent) *ContentGraph {
	graph := &ContentGraph{
		nodes: make(map[string]*ContentNode),
	}

	// Add all nodes
	for _, content := range contents {
		node := &ContentNode{
			identifier: content.Identifier,
			next:       []string{},
		}

		// Add regular next
		if content.Next != "" {
			node.next = append(node.next, content.Next)
		}

		// Add choices
		for _, choice := range content.Choices {
			if choice.Next != "" {
				node.next = append(node.next, choice.Next)
			}
		}

		graph.nodes[content.Identifier] = node
	}

	// Add special reward nodes
	graph.nodes["coin"] = &ContentNode{
		identifier:     "coin",
		canReachReward: true, // Rewards can reach themselves
	}
	graph.nodes["free"] = &ContentNode{
		identifier:     "free",
		canReachReward: true, // "free" is also a valid endpoint
	}

	return graph
}

// DetectCycles checks if there are cycles in the graph
func (g *ContentGraph) DetectCycles() []string {
	cycles := []string{}

	// Reset all nodes
	for _, node := range g.nodes {
		node.visited = false
		node.inPath = false
	}

	for id, node := range g.nodes {
		if !node.visited {
			path := []string{}
			if g.dfs(id, path) {
				cycles = append(cycles, id)
			}
		}
	}

	return cycles
}

// dfs performs depth-first search for cycle detection
func (g *ContentGraph) dfs(id string, path []string) bool {
	node, exists := g.nodes[id]
	if !exists {
		return false // Node doesn't exist
	}

	if node.inPath {
		// Found a cycle
		return true
	}

	if node.visited {
		return false
	}

	node.visited = true
	node.inPath = true
	path = append(path, id)

	for _, nextID := range node.next {
		if g.dfs(nextID, path) {
			return true
		}
	}

	node.inPath = false
	return false
}

// CheckReachability checks if all nodes can reach a reward
func (g *ContentGraph) CheckReachability() []string {
	unreachable := []string{}

	// Reset visited flags
	for _, node := range g.nodes {
		node.visited = false
	}

	// Compute reachability
	g.computeReachability()

	// Find unreachable nodes
	for id, node := range g.nodes {
		// Skip special nodes (coin, free)
		if id == "coin" || id == "free" {
			continue
		}

		// Add unreachable nodes to the list
		if !node.canReachReward {
			unreachable = append(unreachable, id)
		}
	}

	return unreachable
}

// computeReachability computes if nodes can reach a reward
func (g *ContentGraph) computeReachability() {
	// Iterate until no changes
	changed := true
	for changed {
		changed = false

		for _, node := range g.nodes {
			if node.canReachReward {
				continue // Already known to reach reward
			}

			// Check if any next node can reach reward
			for _, nextID := range node.next {
				// Special case for rewards and valid endpoints
				if nextID == "coin" || nextID == "free" {
					node.canReachReward = true
					changed = true
					break
				}

				nextNode, exists := g.nodes[nextID]
				if exists && nextNode.canReachReward {
					node.canReachReward = true
					changed = true
					break
				}
			}
		}
	}
}

// validateChallengeChoiceType validates that a challenge choice has a valid type
// and recursively validates any nested choices
func validateChallengeChoiceType(t *testing.T, trailId, phaseId, contentId string, choice *content.ChallengeChoice) []string {
	var issues []string

	// Get line number for better error reporting
	lineNum := getContentLineNumberFromMetadata(trailId, contentId)
	lineInfo := ""
	if lineNum > 0 {
		lineInfo = fmt.Sprintf(" (line %d)", lineNum)
	}

	// Check if the choice has a type
	if choice.Type == "" {
		t.Logf("Warning: Choice %s in content %s is missing required 'type' field%s", choice.Identifier, contentId, lineInfo)
		issues = append(issues, fmt.Sprintf("Trail %s, Phase %s, Content %s, Choice %s is missing required 'type' field%s",
			trailId, phaseId, contentId, choice.Identifier, lineInfo))
	} else {
		// Check if the type is valid
		validTypes := []string{"TEXT", "NUMBER", "OPTIONS", "DATE"}
		isValid := false
		for _, validType := range validTypes {
			if choice.Type == validType {
				isValid = true
				break
			}
		}

		if !isValid {
			t.Logf("Warning: Choice %s in content %s has invalid type '%s' (must be one of TEXT, NUMBER, OPTIONS, DATE)%s",
				choice.Identifier, contentId, choice.Type, lineInfo)
			issues = append(issues, fmt.Sprintf("Trail %s, Phase %s, Content %s, Choice %s has invalid type '%s' (must be one of TEXT, NUMBER, OPTIONS, DATE)%s",
				trailId, phaseId, contentId, choice.Identifier, choice.Type, lineInfo))
		}
	}

	// Recursively validate nested choices
	for _, nestedChoice := range choice.Choices {
		nestedIssues := validateChallengeChoiceType(t, trailId, phaseId, contentId, nestedChoice)
		issues = append(issues, nestedIssues...)
	}

	return issues
}
