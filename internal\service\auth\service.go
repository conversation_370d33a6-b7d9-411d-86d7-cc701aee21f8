package auth

import (
	"context"
	"encoding/base64"
	"mime/multipart"
	"net/url"
	"os"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/service/notification"
	"github.com/dsoplabs/dinbora-backend/internal/service/s3"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
)

type Service interface {
	// User Auth
	Register(context.Context, *model.User, *multipart.FileHeader, string) (*token.Token, error)
	LegacyRegister(context.Context, *model.User, string) (*token.Token, error)
	Login(context.Context, *model.User) (*model.User, string, *token.Token, error)
	RefreshAuth(context.Context, string) (*model.User, string, *token.Token, error)
	ForgotPassword(context.Context, string) error
	ResetPassword(context.Context, string, string) error
	CheckPassword(context.Context, string, string) error

	// Admin Auth
	AdminLogin(context.Context, *model.User) (*token.Token, error)
}

type service struct {
	UserService         user.Service
	NotificationService *notification.Service
	S3Service           s3.Service
}

func New(userService user.Service, notificationService *notification.Service, s3Service s3.Service) Service {
	return &service{
		UserService:         userService,
		NotificationService: notificationService,
		S3Service:           s3Service,
	}
}

// User Auth
func (s *service) Register(ctx context.Context, user *model.User, photo *multipart.FileHeader, referralCode string) (*token.Token, error) {
	if photo != nil {
		photoURL, err := s.S3Service.UploadFile(ctx, photo, os.Getenv("AWS_S3_USER_PHOTOS_FOLDER"))
		if err != nil {
			return nil, errors.New(errors.Service, "failed to upload photo", errors.Internal, err)
		}
		user.PhotoURL = photoURL
	}

	if err := user.PrepareCreate(); err != nil {
		return nil, err
	}

	if err := s.UserService.Create(ctx, user, referralCode); err != nil {
		return nil, err
	}

	createdUser, err := s.UserService.FindByEmail(ctx, user.Email)
	if err != nil {
		// Attempt to find by ID if email search fails or if email is not unique before creation
		// This part might need adjustment based on how UserService.Create handles user ID generation
		// For now, we assume FindByEmail is the primary way post-creation.
		return nil, errors.New(errors.Service, "failed to retrieve user after creation", errors.NotFound, err)
	}

	userToken, err := token.Create(createdUser)
	if err != nil {
		return nil, errors.New(errors.Service, "failed to create token", errors.Internal, err)
	}

	return userToken, nil
}

func (s *service) LegacyRegister(ctx context.Context, user *model.User, referralCode string) (*token.Token, error) {
	if err := user.PrepareCreate(); err != nil {
		return nil, err
	}

	if err := s.UserService.Create(ctx, user, referralCode); err != nil {
		return nil, err
	}
	createdUser, err := s.UserService.FindByEmail(ctx, user.Email)
	if err != nil {
		return nil, err
	}

	userToken, err := token.Create(createdUser)
	if err != nil {
		return nil, err
	}

	return userToken, nil
}

func (s *service) Login(ctx context.Context, user *model.User) (*model.User, string, *token.Token, error) {
	if err := user.PrepareLogin(); err != nil {
		return nil, "", nil, err
	}

	userData, err := s.UserService.FindByEmail(ctx, user.Email)
	if err != nil {
		return nil, "", nil, err
	}
	userData.ID = userData.ObjectID.Hex()
	walletID, err := s.UserService.FindUserWallet(ctx, userData.ID)
	if err != nil {
		return nil, "", nil, err
	}

	if err = userData.VerifyPassword(userData.Password, user.Password); err != nil {
		return nil, "", nil, err
	}

	token, err := token.Create(userData)
	if err != nil {
		return nil, "", nil, err
	}

	return userData, walletID, token, nil
}

func (s *service) RefreshAuth(ctx context.Context, refreshToken string) (*model.User, string, *token.Token, error) {
	tokenDetails, err := token.GetClaimsFromRefreshToken(refreshToken)
	if err != nil {
		return nil, "", nil, err
	}

	if err = tokenDetails.Validate(); err != nil {
		return nil, "", nil, err
	}

	user, err := s.UserService.Find(ctx, tokenDetails.Uid)
	if err != nil {
		return nil, "", nil, err
	}
	user.ID = user.ObjectID.Hex()
	walletID, err := s.UserService.FindUserWallet(ctx, user.ID)
	if err != nil {
		return nil, "", nil, err
	}

	token, err := token.Create(user)
	if err != nil {
		return nil, "", nil, err
	}

	return user, walletID, token, nil
}

func (s *service) ForgotPassword(ctx context.Context, userEmail string) error {
	foundUser, err := s.UserService.FindByEmail(ctx, userEmail)
	if err != nil {
		return err
	}

	userToken, err := token.Standalone(foundUser)
	if err != nil {
		return err
	}

	appURL := os.Getenv("APP_URL")
	if appURL == "" {
		appURL = "http://localhost:8080" // Fallback for development
	}
	resetLink := appURL + "/nova-senha/" + url.QueryEscape(base64.StdEncoding.WithPadding(base64.NoPadding).EncodeToString([]byte(userToken)))

	if s.NotificationService.BrevoNotifier != nil {
		return s.NotificationService.BrevoNotifier.SendPasswordReset(ctx, foundUser.Email, foundUser.Name, resetLink)
	}

	return errors.New(errors.Service, "brevo notifier not available", errors.Internal, nil)
}

func (s *service) ResetPassword(ctx context.Context, resetToken, newPassword string) error {
	decodedToken, err := base64.StdEncoding.WithPadding(base64.NoPadding).DecodeString(resetToken)
	if err != nil {
		return err
	}

	details, err := token.GetClaimsFromToken(string(decodedToken[:]))
	if err != nil {
		return err
	}

	foundUser, err := s.UserService.Find(ctx, details.Uid)
	if err != nil {
		return err
	}

	// Copying foundUser data to newUser and then changing the password
	newUser := *foundUser
	newUser.Password = newPassword

	if err = foundUser.PrepareUpdate(&newUser); err != nil {
		return err
	}

	if err = s.UserService.Patch(ctx, foundUser, &newUser); err != nil {
		return err
	}

	return nil
}

func (s *service) CheckPassword(ctx context.Context, userUid, password string) error {
	foundUser, err := s.UserService.Find(ctx, userUid)
	if err != nil {
		return err
	}

	return foundUser.VerifyPassword(foundUser.Password, password)
}

// Admin Auth
func (s *service) AdminLogin(ctx context.Context, user *model.User) (*token.Token, error) {
	if err := user.PrepareLogin(); err != nil {
		return nil, err
	}

	foundUser, err := s.UserService.FindByEmail(ctx, user.Email)
	if err != nil {
		return nil, err
	}

	if err = foundUser.VerifyPassword(foundUser.Password, user.Password); err != nil {
		return nil, err
	}

	if !foundUser.IsAdmin() {
		return nil, errors.New(errors.Service, "user is not an admin", errors.Unauthorized, nil)
	}

	userToken, err := token.Create(foundUser)
	if err != nil {
		return nil, err
	}

	return userToken, nil
}
