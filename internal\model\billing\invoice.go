package billing

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

type Invoice struct {
	ObjectID             primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID                   string             `json:"id,omitempty" bson:"-"`
	Contract             string             `json:"contract" bson:"contract"`
	ClientSecret         string             `json:"clientSecret" bson:"clientSecret"`
	PaymentPage          string             `json:"paymentPage" bson:"paymentPage"`
	ContractExternalCode string             `json:"contractExternalCode" bson:"contractExternalCode"`
	Plan                 string             `json:"plan" bson:"plan"`
	StartDate            time.Time          `json:"startDate" bson:"startDate"`
	EndDate              time.Time          `json:"endDate" bson:"endDate"`
	CreatedAt            time.Time          `json:"createdAt" bson:"createdAt"`
	Status               string             `json:"status" bson:"status"`
	Pricing              *Pricing           `json:"pricing" bson:"pricing"`
	ExternalCode         string             `json:"externalCode" bson:"externalCode"`
}

type FilterParams struct {
	Customer string `json:"customer" bson:"customer"`
	Status   string `json:"status" bson:"status"`
	Contract string `json:"contract" bson:"contract"`
}
