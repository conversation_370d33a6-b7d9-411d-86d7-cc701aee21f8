package billing

type Subscription struct {
	IntendedPlan string `json:"intendedPlan" bson:"intendedPlan"`
}

type SubscriptionParams struct {
	ID           string `json:"id" bson:"id"`
	ClientSecret string `json:"clientSecret" bson:"clientSecret"`
	PaymentPage  string `json:"paymentPage" bson:"paymentPage"`
}

type SubscriptionCancelParams struct {
	Contract string `json:"contract" bson:"contract"`
}
