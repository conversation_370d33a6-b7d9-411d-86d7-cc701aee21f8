package api

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/auth"
	"golang.org/x/sync/errgroup"
	"google.golang.org/api/option"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/dsoplabs/dinbora-backend/config"
	"github.com/dsoplabs/dinbora-backend/data/mongodb"
	"github.com/dsoplabs/dinbora-backend/internal/api/controller"
	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/repository"
	"github.com/dsoplabs/dinbora-backend/internal/api/service"
	"github.com/dsoplabs/dinbora-backend/internal/api/validators"
	"github.com/dsoplabs/dinbora-backend/internal/migrator"
	"github.com/dsoplabs/dinbora-backend/internal/migrator/content"
	"github.com/dsoplabs/dinbora-backend/internal/migrator/migrations"
	"github.com/dsoplabs/dinbora-backend/internal/scheduler"
	"github.com/sendgrid/sendgrid-go"
	"github.com/stripe/stripe-go/v72/client"
)

// Server contains the server structure, routes, configurations, and database.
type Server struct {
	ctx      context.Context
	echo     *echo.Echo
	db       *mongo.Database
	sendgrid *sendgrid.Client
	stripe   *client.API
	firebase *firebase.App
}

// NewServer configures the server.
func New() (*Server, error) {
	ctx := context.Background()

	// Configuration must be loaded first as other operations depend on it
	if err := loadConfiguration(); err != nil {
		return nil, fmt.Errorf("configuration loading failed: %w", err)
	}

	// Initialize variables for concurrent operations
	var (
		db             *mongo.Database
		stripeClient   *client.API
		sendgridClient *sendgrid.Client
		firebaseApp    *firebase.App
	)

	// Use errgroup for concurrent initialization
	g, gCtx := errgroup.WithContext(ctx)

	// Connect to MongoDB concurrently
	g.Go(func() error {
		var errConnect error
		db, errConnect = connectToMongoDB(gCtx) // Pass group context
		if errConnect != nil {
			return fmt.Errorf("mongoDB connection failed: %w", errConnect)
		}
		return nil
	})

	// Initialize external clients concurrently
	g.Go(func() error {
		var errInit error
		stripeClient, sendgridClient, firebaseApp, errInit = initializeClients()
		if errInit != nil {
			return fmt.Errorf("client initialization failed: %w", errInit)
		}
		return nil
	})

	// Wait for all goroutines to complete and check for errors
	if err := g.Wait(); err != nil {
		return nil, err // Error already wrapped
	}

	// Create and return server instance
	return &Server{
		ctx:      ctx,
		db:       db,
		echo:     echo.New(),
		sendgrid: sendgridClient,
		stripe:   stripeClient,
		firebase: firebaseApp,
	}, nil
}

// Start initializes the server and handles graceful shutdown.
func (s *Server) Start() error {
	server := &http.Server{
		Addr:    os.Getenv("API_PORT"),
		Handler: s.echo, // Echo instance will be the handler
	}

	// Apply middlewares and router configuration
	s.echo.HTTPErrorHandler = middlewares.NewHttpErrorHandler().Handler
	s.echo.Use(middlewares.CustomLogger())
	s.echo.Use(middleware.Recover())
	s.echo.Use(middleware.GzipWithConfig(middleware.GzipConfig{
		Skipper: func(c echo.Context) bool {
			return strings.Contains(c.Path(), "/health")
		},
	}))

	// Register validator
	s.echo.Validator = validators.NewCustomValidator()
	// Define allowed origins
	allowOrigins := []string{
		"https://dinbora.com.br", "https://www.dinbora.com.br", "https://d39sctkd9dpikc.cloudfront.net/",
	}
	// If in test mode, add the test domain to the list of allowed origins
	if os.Getenv("API_MODE") != "Production" {
		allowOrigins = append(allowOrigins, "http://localhost", "http://localhost:3000", "http://localhost:3005", "https://dinbora.binaryaces.dev", "http://localhost:5173")
	}
	s.echo.Use(middleware.CORSWithConfig(middleware.CORSConfig{
		AllowOrigins:     allowOrigins,
		AllowMethods:     []string{http.MethodGet, http.MethodHead, http.MethodPut, http.MethodPatch, http.MethodPost, http.MethodDelete},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization", "Access-Control-Allow-Origin"},
		AllowCredentials: true,
		MaxAge:           300,
	}))
	s.echo.Use(middleware.SecureWithConfig(middlewares.SecureConfig()))

	if err := s.Router(); err != nil {
		return fmt.Errorf("router initialization failed: %w", err)
	}

	errChan := make(chan error, 1)

	// Start server in a goroutine
	go func() {
		log.Printf("Starting server on %s", server.Addr)
		if err := s.echo.StartServer(server); err != nil && err != http.ErrServerClosed {
			errChan <- fmt.Errorf("server start failed: %w", err)
		} else if err == http.ErrServerClosed {
			log.Println("Server closed normally.")
		}
	}()

	// Setup signal handling for graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt, syscall.SIGTERM)

	var finalErr error

	// Wait for interrupt signal or server error
	select {
	case err := <-errChan:
		log.Printf("Server error: %v", err)
		finalErr = err
	case sig := <-quit:
		log.Printf("Received signal: %s. Shutting down server...", sig)
	}

	// Graceful shutdown for Echo server
	shutdownCtx, cancelShutdown := context.WithTimeout(s.ctx, 15*time.Second) // Use server context s.ctx
	defer cancelShutdown()
	if err := s.echo.Shutdown(shutdownCtx); err != nil {
		log.Printf("Echo server shutdown failed: %v", err)
		if finalErr == nil { // Don't overwrite an existing error from server start
			finalErr = fmt.Errorf("echo server shutdown failed: %w", err)
		}
	} else {
		log.Println("Echo server gracefully stopped.")
	}

	// Graceful shutdown for MongoDB
	mongoShutdownCtx, cancelMongo := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancelMongo()
	mongodb.DisconnectClient(mongoShutdownCtx) // DisconnectClient does not return an error to check here. Logging is internal.
	log.Println("MongoDB client disconnection process initiated.")

	return finalErr
}

// Router creates routing groups for the server. And initializes the repositories and controllers of the entities.
func (s *Server) Router() error {
	// Initialize containers and their components
	var (
		errSync    error
		errMigrate error
	)

	// Initialize base components sequentially since they depend on each other
	repoContainer := repository.NewContainer(s.db, s.stripe)
	repository := repoContainer.Initialize()

	serviceContainer := service.NewContainer(repository, s.sendgrid, s.firebase)
	service := serviceContainer.Initialize()

	controllerContainer := controller.NewContainer(service)
	controller := controllerContainer.Initialize()

	// Initialize and start the scheduler for monthly snapshots
	dashboardScheduler := scheduler.New(service.Dashboard, service.User)
	if err := dashboardScheduler.Start(); err != nil {
		log.Printf("Warning: Failed to start dashboard scheduler: %v", err)
	} else {
		log.Println("Dashboard scheduler started successfully")
	}

	// Use WaitGroup for concurrent operations
	var wg sync.WaitGroup
	wg.Add(2) // Two concurrent operations: product sync/routes and migrations

	// Handle product sync and route registration concurrently
	go func() {
		defer wg.Done()
		var syncWg sync.WaitGroup
		syncWg.Add(1)

		// Sync products to Stripe
		go func() {
			defer syncWg.Done()
			if err := service.Product.SyncAll(s.ctx); err != nil {
				errSync = fmt.Errorf("failed to sync products: %w", err)
			}
		}()

		// Register routes (can be done while products sync)
		if err := s.RegisterRoutes(s.ctx, controller.GetAll()...); err != nil {
			errSync = fmt.Errorf("failed to register routes: %w", err)
		}

		syncWg.Wait()
	}()

	// Run migrations concurrently with the above operations
	go func() {
		defer wg.Done()
		if err := runMigrations(s.db, service); err != nil {
			errMigrate = fmt.Errorf("migration failed: %w", err)
		}
	}()

	// Wait for all operations to complete
	wg.Wait()

	// Check for errors
	if errSync != nil {
		return errSync
	}
	if errMigrate != nil {
		return errMigrate
	}

	log.Printf("Routes registered successfully.")
	return nil
}

// --- Helper functions for New() ---
func loadConfiguration() error {
	return config.Env() // Or config.Load(configPath) if you uncomment that section
}

func connectToMongoDB(ctx context.Context) (*mongo.Database, error) {
	// Use a context with timeout for the connection attempt, derived from the passed context
	connectCtx, cancel := context.WithTimeout(ctx, 15*time.Second) // e.g., 15 seconds
	defer cancel()

	// Call the singleton connector
	mongoDB, err := mongodb.Connect(connectCtx) // Pass context
	if err != nil {
		// Error is already logged within mongodb.Connect
		return nil, fmt.Errorf("singleton mongoDB connection failed: %w", err)
	}

	// Log success here or rely on logging within mongodb.Connect
	// log.Printf("mongoDB: connected to %s database.", mongoDB.Name()) // Optional redundancy
	return mongoDB, nil
}

func initializeClients() (*client.API, *sendgrid.Client, *firebase.App, error) {
	stripeClient := &client.API{}
	stripeClient.Init(os.Getenv("STRIPE_SECRET_KEY"), nil)

	sendgridClient := sendgrid.NewSendClient(os.Getenv("SENDGRID_API_KEY"))

	opt := option.WithCredentialsJSON([]byte(os.Getenv("FIREBASE_CREDENTIALS_JSON")))

	firebaseApp, err := firebase.NewApp(context.Background(), nil, opt)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("failed to initialize firebase client: %w", err)
	}

	// --- NEW: Verification Step ---
	log.Println("Attempting to verify Firebase connection...")
	authClient, err := firebaseApp.Auth(context.Background())
	if err != nil {
		return nil, nil, nil, fmt.Errorf("failed to create firebase auth client for verification: %w", err)
	}

	// Try to get a user that certainly does not exist.
	// This forces an authenticated API call to Google's servers.
	_, err = authClient.GetUser(context.Background(), "a-non-existent-user-uid-for-testing")

	// Check the specific error.
	if auth.IsUserNotFound(err) {
		// THIS IS THE SUCCESS CASE!
		// It means we connected and authenticated successfully, and Firebase correctly
		// told us the user doesn't exist.
		log.Println("Firebase connection successfully verified.")
	} else if err != nil {
		// Any other error means a problem with credentials, permissions, or network.
		return nil, nil, nil, fmt.Errorf("failed to verify firebase connection (check credentials and permissions): %w", err)
	}

	return stripeClient, sendgridClient, firebaseApp, nil
}

// # Note: The JSON content must be a single line. You can use an online tool to minify it.
// FIREBASE_CREDENTIALS_JSON='{"type": "service_account", "project_id": "...", ...}'
// // Get the JSON string from the environment variable
//     firebaseCreds := os.Getenv("FIREBASE_CREDENTIALS_JSON")
//     if firebaseCreds == "" {
//         return nil, nil, nil, fmt.Errorf("FIREBASE_CREDENTIALS_JSON environment variable not set")
//     }

//     // Use `option.WithCredentialsJSON` to pass the raw JSON bytes
//     opt := option.WithCredentialsJSON([]byte(firebaseCreds))
//     firebaseApp, err := firebase.NewApp(context.Background(), nil, opt)
//     if err != nil {
//         return nil, nil, nil, fmt.Errorf("failed to initialize firebase client with JSON credentials: %w", err)
//     }

// --- Helper functions for Start() ---
func runMigrations(db *mongo.Database, service *service.ServiceRegistry) error {
	// Initialize migration manager
	manager := migrator.NewMigrationManager(db)

	// Register all migrations
	ctx := context.Background()

	manager.AddMigration(migrations.NewContentMigration(db, content.CreateOrUpdate, nil)) // Passing nil for collections means process all
	manager.AddMigration(migrations.NewUpdateFinancialSheetCategories(db))
	manager.AddMigration(migrations.NewUpdateDreamboardCategories(db))
	manager.AddMigration(migrations.NewUpdateFinancialDNAMemberIcon(db))
	manager.AddMigration(migrations.NewMigrateGroupsToRolesMigration(db))
	//manager.AddMigration(migrations.NewCreateReferralsMigration(db))
	//manager.AddMigration(migrations.NewCreateDefaultDreamboardsMigration(db))
	//manager.AddMigration(migrations.NewComputeTotals(db))
	//manager.AddMigration(migrations.NewCreateCategoriesDreamboards(db))
	//manager.AddMigration(migrations.NewCreateDefaultFinancialsheetsMigration(db, service.FinancialSheet))

	if err := manager.Run(ctx); err != nil {
		return fmt.Errorf("migration failed: %w", err)
	}

	return nil
}
