package billing

import (
	"strings"
	"time"

	"github.com/imdario/mergo"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	SUBSCRIPTION = Type{
		Name:       "Plan",
		Identifier: "plan",
	}
)

type Type struct {
	Name       string `json:"name" bson:"name"`
	Identifier string `json:"identifier" bson:"identifier"`
}

type Feature struct {
	Name       string `json:"name" bson:"name"`
	Identifier string `json:"identifier" bson:"identifier"`
}

type Installment struct {
	Value float64 `json:"value" bson:"value"`
	Order uint8   `json:"order" bson:"order"`
}

type Pricing struct {
	Value        float64 `json:"value" bson:"value"`
	ExternalCode string  `json:"externalCode" bson:"externalCode"`
}

type Product struct {
	ObjectID         primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID               string             `json:"id,omitempty" bson:"-"`
	Name             string             `json:"name" bson:"name"`
	Identifier       string             `json:"identifier" bson:"identifier"`
	Description      string             `json:"description" bson:"description"`
	Logo             string             `json:"logo,omitempty" bson:"logo,omitempty"`
	Type             *Type              `json:"type" bson:"type"`
	Features         []*Feature         `json:"features" bson:"features"`
	Provisioning     *Provisioning      `json:"provisioning" bson:"provisioning"`
	Pricing          *Pricing           `json:"pricing" bson:"pricing"`
	Installments     []*Installment     `json:"installments,omitempty" bson:"installments,omitempty"`
	MarketingChannel bool               `json:"marketingChannel" bson:"marketingChannel"`
	CreatedAt        time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt        time.Time          `json:"updatedAt" bson:"updatedAt"`
}

type Provisioning struct {
	Wallet string `json:"wallet" bson:"wallet"`
}

func (p *Product) Sanitize() *Product {
	p.ID = p.ObjectID.Hex()

	return p
}

func (p *Product) PrepareCreate() error {

	p.Name = strings.TrimSpace(p.Name)
	p.Identifier = strings.TrimSpace(strings.ToLower(p.Identifier))
	p.Logo = strings.TrimSpace(p.Logo)

	if err := p.ValidateCreate(); err != nil {
		return err
	}

	if p.Features != nil {
		for _, feature := range p.Features {
			if err := feature.PrepareCreate(); err != nil {
				return err
			}
		}
	}

	if err := p.Type.PrepareCreate(); err != nil {
		return err
	}

	if p.Installments != nil {
		for _, installment := range p.Installments {
			if err := installment.PrepareCreate(); err != nil {
				return err
			}
		}
	}

	p.CreatedAt = time.Now()
	p.UpdatedAt = p.CreatedAt

	return nil

}

func (p *Product) ValidateCreate() error {
	if p.Name == "" {
		return ErrProductRequiredName
	}

	if p.Identifier == "" {
		return ErrProductRequiredIdentifier
	}

	if p.Type == nil {
		return ErrProductRequiredType
	}

	if p.Features == nil {
		return ErrProductRequiredFeatures
	}

	if p.Pricing == nil {
		return ErrProductRequiredPricing
	}

	if p.Pricing.Value < 0 {
		return ErrProductInvalidValue
	}

	return nil
}

func (p *Product) PrepareUpdate(newProduct *Product) error {
	if err := mergo.Merge(p, newProduct, mergo.WithOverride); err != nil {
		return err
	}

	p.Name = strings.TrimSpace(p.Name)
	p.Identifier = strings.TrimSpace(strings.ToLower(p.Identifier))
	p.Logo = strings.TrimSpace(p.Logo)

	if err := p.ValidateUpdate(); err != nil {
		return err
	}

	if p.Features != nil {
		for _, feature := range p.Features {
			if err := feature.PrepareUpdate(); err != nil {
				return err
			}
		}
	}

	if err := p.Type.PrepareUpdate(); err != nil {
		return err
	}

	if p.Installments != nil {
		for _, installment := range p.Installments {
			if err := installment.PrepareUpdate(); err != nil {
				return err
			}
		}
	}

	p.UpdatedAt = time.Now()

	return nil
}

func (p *Product) ValidateUpdate() error {
	if p.ID == "" {
		if !p.ObjectID.IsZero() {
			p.ID = p.ObjectID.Hex()
		} else {
			return ErrProductInvalidID
		}
	} else {
		productId, err := primitive.ObjectIDFromHex(p.ID)
		if err != nil {
			return err
		}
		p.ObjectID = productId
	}

	return p.ValidateCreate()
}

func (t *Type) PrepareCreate() error {

	t.Name = strings.TrimSpace(t.Name)
	t.Identifier = strings.TrimSpace(strings.ToLower(t.Identifier))

	if err := t.ValidateCreate(); err != nil {
		return err
	}

	return nil
}

func (t *Type) ValidateCreate() error {
	if t.Name == "" {
		return ErrTypeRequiredName
	}

	if t.Identifier == "" {
		return ErrTypeRequiredIdentifier
	}

	return nil
}

func (t *Type) PrepareUpdate() error {

	t.Name = strings.TrimSpace(t.Name)
	t.Identifier = strings.TrimSpace(strings.ToLower(t.Identifier))

	if err := t.ValidateUpdate(); err != nil {
		return err
	}

	return nil
}

func (t *Type) ValidateUpdate() error {
	return t.ValidateCreate()
}

func (f *Feature) PrepareCreate() error {

	f.Name = strings.TrimSpace(f.Name)
	f.Identifier = strings.TrimSpace(strings.ToLower(f.Identifier))

	if err := f.ValidateCreate(); err != nil {
		return err
	}

	return nil
}

func (f *Feature) ValidateCreate() error {
	if f.Name == "" {
		return ErrFeatureRequiredName
	}

	if f.Identifier == "" {
		return ErrFeatureRequiredIdentifier
	}

	return nil
}

func (f *Feature) PrepareUpdate() error {

	f.Name = strings.TrimSpace(f.Name)
	f.Identifier = strings.TrimSpace(strings.ToLower(f.Identifier))

	if err := f.ValidateUpdate(); err != nil {
		return err
	}

	return nil
}

func (f *Feature) ValidateUpdate() error {
	return f.ValidateCreate()
}

func (i *Installment) PrepareCreate() error {
	return i.ValidateCreate()
}

func (i *Installment) ValidateCreate() error {
	if i.Value < 0 {
		return ErrInstallmentInvalidValue
	}

	if i.Order <= 0 {
		return ErrInstallmentInvalidOrder
	}

	return nil
}

func (i *Installment) PrepareUpdate() error {
	return i.ValidateUpdate()
}

func (i *Installment) ValidateUpdate() error {
	return i.ValidateCreate()
}
