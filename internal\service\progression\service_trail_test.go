package progression

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Test progression using a real trail file
func TestProgressionWithRealTrail(t *testing.T) {
	// Create mocks
	mockRepo := new(MockProgressionRepo)
	mockTrailSvc := new(MockTrailSvc)
	mockVaultSvc := new(MockVaultSvc)
	mockContractSvc := new(MockContractSvc)
	mockCacheSvc := new(MockCacheSvc)

	// Create service
	service := New(
		mockRepo,
		mockTrailSvc,
		mockVaultSvc,
		mockContractSvc,
		mockCacheSvc,
		nil,
	)

	// Create test data
	ctx := context.Background()
	userId := "test-user-id"
	trailId := "67f6ddf3181babca8896e73c" // Using a real trail ID from the migration folder

	// Create a simple trail for testing
	mockTrail := &content.Trail{
		ID:           trailId,
		Name:         "Test Trail",
		Identifier:   "test-trail",
		Level:        1,
		Logo:         "test-logo.png",
		Color:        "#FFFFFF",
		Requirements: []string{},
		Lessons: []*content.Lesson{
			{
				Name:         "Lesson 1",
				Identifier:   "lesson-1",
				Logo:         "lesson-1-logo.png",
				Color:        "#FF0000",
				Order:        1,
				Requirements: []string{},
				Content: []*content.LessonContent{
					{
						Image:       "image-1.png",
						Identifier:  "lesson-1-content-1",
						Description: "First content of lesson 1",
						Next:        "lesson-1-content-2",
					},
					{
						Image:       "image-2.png",
						Identifier:  "lesson-1-content-2",
						Description: "Second content of lesson 1",
						Next:        "coin", // Completion marker
					},
				},
			},
		},
		Challenge: &content.Challenge{
			Name:        "Test Challenge",
			Identifier:  "test-challenge",
			Description: "Test challenge description",
			Logo:        "challenge-logo.png",
			Color:       "#0000FF",
			Locked:      false,
			Phases: []*content.ChallengePhase{
				{
					Name:         "Phase 1",
					Order:        1,
					Identifier:   "phase-1",
					Requirements: []string{},
					Content: []*content.ChallengeContent{
						{
							Image:       "phase-1-image-1.png",
							Identifier:  "phase-1-content-1",
							Description: "First content of phase 1",
							Next:        "phase-1-content-2",
						},
						{
							Image:       "phase-1-image-2.png",
							Identifier:  "phase-1-content-2",
							Description: "Second content of phase 1",
							Next:        "coin", // Completion marker
						},
					},
				},
			},
		},
	}

	// Print some info about the trail for debugging
	t.Logf("Trail ID: %s, Name: %s", mockTrail.ID, mockTrail.Name)
	t.Logf("Number of lessons: %d", len(mockTrail.Lessons))
	t.Logf("Challenge name: %s", mockTrail.Challenge.Name)

	// Create mock vault
	mockVault := &model.Vault{
		ObjectID:  primitive.NewObjectID(),
		User:      userId,
		Coins:     0,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Create mock progression with an initial trail structure
	// This is needed because the service expects certain fields to be initialized
	lessonContent := &progression.LessonContent{
		Identifier: "initial-content",
		Choice: &progression.ModuleContentChoice{
			Identifier: "initial-content",
			Next:       "next-content",
		},
		Timestamp: time.Now(),
	}

	lessonProgression := &progression.Lesson{
		Identifier: mockTrail.Lessons[0].Identifier,
		Path:       []*progression.LessonContent{lessonContent},
		Current:    "next-content",
		Completed:  false,
		Available:  true,
		Rewarded:   false,
	}

	trailProgression := &progression.Trail{
		ID: trailId,
		Lessons: []*progression.Lesson{
			lessonProgression,
		},
		Current:   mockTrail.Lessons[0].Identifier,
		Total:     0,
		Available: true,
	}

	mockProgression := &progression.Progression{
		ObjectID:  primitive.NewObjectID(),
		User:      userId,
		Trails:    []*progression.Trail{trailProgression},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Setup mocks
	mockTrailSvc.On("Find", ctx, trailId).Return(mockTrail, nil)
	mockVaultSvc.On("FindByUser", ctx, userId).Return(mockVault, nil)

	// Cache expectations - simulate cache miss for FindByUser
	mockCacheSvc.On("Get", ctx, "progression:user:test-user-id").Return(nil, false)
	mockCacheSvc.On("Set", ctx, "progression:user:test-user-id", mockProgression, time.Hour).Return(nil)
	// Cache invalidation on update
	mockCacheSvc.On("Delete", ctx, "progression:user:test-user-id").Return(nil)

	mockRepo.On("FindByUser", ctx, userId).Return(mockProgression, nil)
	mockVaultSvc.On("Update", ctx, mock.Anything).Return(nil)
	mockRepo.On("Update", ctx, mock.Anything).Return(nil)

	// Test progression through the first lesson and verify rewards
	t.Run("Lesson Progress and Rewards", func(t *testing.T) {
		// Get the first lesson from the trail
		if len(mockTrail.Lessons) == 0 {
			t.Skip("No lessons found in the trail")
		}

		firstLesson := mockTrail.Lessons[0]
		t.Logf("Testing lesson: %s (%s)", firstLesson.Name, firstLesson.Identifier)

		// Check if the lesson has content
		if len(firstLesson.Content) == 0 {
			t.Skip("No content found in the first lesson")
		}

		// Store initial coin count
		initialCoins := mockVault.Coins
		t.Logf("Initial coins: %d", initialCoins)

		// Progress through each content in the lesson
		for i, content := range firstLesson.Content {
			t.Logf("Processing content %d: %s", i, content.Identifier)

			// Determine the next content
			var nextContent string
			if i < len(firstLesson.Content)-1 {
				nextContent = firstLesson.Content[i+1].Identifier
			} else {
				nextContent = "coin" // Last content completes the lesson
			}

			// Create progression body
			progressionBody := &progression.ProgressionBody{
				Trail:   trailId,
				Module:  firstLesson.Identifier,
				Content: content.Identifier,
				Type:    "LESSON",
				Choice: &progression.ModuleContentChoice{
					Identifier: content.Identifier,
					Next:       nextContent,
				},
			}

			// Initialize the mock progression with the correct current content
			lessonProgression.Current = content.Identifier

			// Register progression
			err := service.CreateLesson(ctx, userId, "standard", []string{}, progressionBody)
			assert.NoError(t, err)

			// For the last content, verify that rewards were given
			if i == len(firstLesson.Content)-1 {
				// Capture the updated vault to check rewards
				updatedVault := mockVault

				// Verify coins were awarded
				t.Logf("Final coins: %d (initial: %d)", updatedVault.Coins, initialCoins)
				assert.Equal(t, initialCoins+1, updatedVault.Coins, "User should receive 1 coin for completing the lesson")

				// Verify lesson is marked as completed and rewarded
				mockRepo.AssertCalled(t, "Update", ctx, mock.Anything)

				// Check that the lesson is marked as completed and rewarded in the progression
				assert.True(t, lessonProgression.Completed, "Lesson should be marked as completed")
				assert.True(t, lessonProgression.Rewarded, "Lesson should be marked as rewarded")
			}
		}
	})

	// Test progression through the challenge if it exists and verify rewards
	t.Run("Challenge Progress and Rewards", func(t *testing.T) {
		if mockTrail.Challenge == nil || len(mockTrail.Challenge.Phases) == 0 {
			t.Skip("No challenge found in the trail")
		}

		t.Logf("Testing challenge: %s (%s)", mockTrail.Challenge.Name, mockTrail.Challenge.Identifier)

		// Setup progression with completed lessons to enable challenge
		var lessonProgressions []*progression.Lesson
		for _, lesson := range mockTrail.Lessons {
			if lesson.Content == nil {
				continue
			}

			lessonContent := make([]*progression.LessonContent, 0)
			for _, content := range lesson.Content {
				if content.Identifier == "" {
					continue
				}

				lessonContent = append(lessonContent, &progression.LessonContent{
					Identifier: content.Identifier,
					Choice: &progression.ModuleContentChoice{
						Identifier: content.Identifier,
						Next:       "next",
					},
					Timestamp: time.Now(),
				})
			}

			lessonProgressions = append(lessonProgressions, &progression.Lesson{
				Identifier: lesson.Identifier,
				Path:       lessonContent,
				Current:    "coin",
				Completed:  true,
				Available:  true,
				Rewarded:   true,
			})
		}

		trailProgression := &progression.Trail{
			ID:               trailId,
			Lessons:          lessonProgressions,
			Current:          mockTrail.Challenge.Identifier,
			Total:            90,
			Available:        true,
			LessonsCompleted: true,
		}

		mockProgression.Trails = []*progression.Trail{trailProgression}

		// Progress through the first phase of the challenge
		firstPhase := mockTrail.Challenge.Phases[0]
		t.Logf("Testing challenge phase: %s (%s)", firstPhase.Name, firstPhase.Identifier)

		// Check if the phase has content
		if len(firstPhase.Content) == 0 {
			t.Skip("No content found in the first challenge phase")
		}

		// Log the content structure
		t.Logf("Challenge phase has %d content items", len(firstPhase.Content))
		for i, content := range firstPhase.Content {
			t.Logf("Content %d: ID=%s, Next=%s", i, content.Identifier, content.Next)
		}

		// Store initial coin count
		initialCoins := mockVault.Coins
		t.Logf("Initial coins before challenge: %d", initialCoins)

		// Create a challenge phase progression to track
		challengePhase := &progression.ChallengePhase{
			Identifier: firstPhase.Identifier,
			Path:       []*progression.ChallengeContent{},
			Current:    firstPhase.Content[0].Identifier,
			Completed:  false,
			Available:  true,
		}

		// Create a challenge progression
		challenge := &progression.Challenge{
			Identifier: mockTrail.Challenge.Identifier,
			Phases:     []*progression.ChallengePhase{challengePhase},
			Current:    firstPhase.Identifier,
			Completed:  false,
			Total:      0,
			Available:  true,
			Rewarded:   false,
		}

		// Add the challenge to the trail progression
		trailProgression.Challenge = challenge

		for i, content := range firstPhase.Content {
			// Skip if content identifier is empty
			if content.Identifier == "" {
				t.Logf("Skipping content item %d because identifier is empty", i)
				continue
			}

			var nextContent string
			if i < len(firstPhase.Content)-1 {
				nextContent = firstPhase.Content[i+1].Identifier
				if nextContent == "" {
					nextContent = "coin" // Use coin if next identifier is empty
				}
			} else {
				nextContent = "coin" // Last content completes the phase
			}

			t.Logf("Creating progression for challenge content %s with next=%s", content.Identifier, nextContent)

			progressionBody := &progression.ProgressionBody{
				Trail:   trailId,
				Module:  firstPhase.Identifier,
				Content: content.Identifier,
				Type:    "CHALLENGE",
				Choice: &progression.ModuleContentChoice{
					Identifier: content.Identifier,
					Next:       nextContent,
				},
			}

			// Update the current content in the challenge phase
			challengePhase.Current = content.Identifier

			err := service.CreateChallenge(ctx, userId, "standard", []string{}, progressionBody)
			assert.NoError(t, err)

			// For the last content, verify that rewards were given
			if i == len(firstPhase.Content)-1 {
				// Capture the updated vault to check rewards
				updatedVault := mockVault

				// Verify coins were awarded (challenge completion gives 10 coins + 1 coin for trail completion)
				t.Logf("Final coins after challenge: %d (initial: %d)", updatedVault.Coins, initialCoins)
				assert.Equal(t, initialCoins+11, updatedVault.Coins, "User should receive 10 coins for challenge + 1 coin for trail completion")

				// Verify challenge is marked as completed and rewarded
				mockRepo.AssertCalled(t, "Update", ctx, mock.Anything)

				// Check that the challenge phase is marked as completed
				assert.True(t, challengePhase.Completed, "Challenge phase should be marked as completed")

				// Check that the challenge is marked as completed and rewarded
				assert.True(t, challenge.Completed, "Challenge should be marked as completed")
				assert.True(t, challenge.Rewarded, "Challenge should be marked as rewarded")
			}
		}
	})
}
