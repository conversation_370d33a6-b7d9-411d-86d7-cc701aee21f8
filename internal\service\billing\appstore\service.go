package appstore

// import (
// 	"context"
// 	"log"
// 	"os"

// 	b64 "encoding/base64"

// 	"github.com/awa/go-iap/appstore/api"
// )

// type Service interface {
// 	ValidateTransaction(context.Context, string) bool
// }

// type service struct {
// 	StoreConfig *api.StoreConfig
// }

// func (s *service) ValidateTransaction(ctx context.Context, transactionID string) bool {
// 	log.Println("ValidateTransaction Service: Starting a new client in App Store")
// 	appstore := api.NewStoreClient(s.StoreConfig)
// 	response, err := appstore.GetTransactionInfo(ctx, transactionID)
// 	if err != nil {
// 		log.Println("ValidateTransaction Service")
// 		log.Println("cannot get appstore transaction information")
// 		log.Println(err)
// 		return false
// 	}

// 	transaction, err := appstore.ParseSignedTransaction(response.SignedTransactionInfo)
// 	if err != nil {
// 		log.Println("ValidateTransaction Service")
// 		log.Println("cannot parse appstore transaction")
// 		log.Println(err)
// 		return false
// 	}

// 	return (transaction.TransactionID == transactionID)
// }

// func New() Service {
// 	mode := (os.Getenv("API_MODE") != "Production")
// 	private_key, err := b64.StdEncoding.DecodeString(os.Getenv("APP_STORE_KEY"))
// 	if err != nil {
// 		log.Println("cannot decode the key")
// 	}

// 	// log.Println("Sandbox mode:", mode)
// 	// // Check the values
// 	// log.Println("KeyContentEnc:", os.Getenv("APP_STORE_KEY")[0:35])
// 	// log.Println("KeyContentDec:", string(private_key)[0:35])
// 	// log.Println("KeyID", os.Getenv("APP_STORE_KEY_ID")[0:4])
// 	// log.Println("BundleID", os.Getenv("APP_STORE_BUNDLE_ID")[0:4])
// 	// log.Println("Issuer", os.Getenv("APP_STORE_ISSUER")[0:4])

// 	return &service{
// 		StoreConfig: &api.StoreConfig{
// 			KeyContent: private_key,
// 			KeyID:      os.Getenv("APP_STORE_KEY_ID"),
// 			BundleID:   os.Getenv("APP_STORE_BUNDLE_ID"),
// 			Issuer:     os.Getenv("APP_STORE_ISSUER"),
// 			Sandbox:    mode,
// 		},
// 	}
// }
